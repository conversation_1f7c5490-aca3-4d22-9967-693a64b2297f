import { useChat } from 'ai/react';       // 直接使用Vercel React Hook [9]
import { customConfig } from '@kit/ai';  // 引用业务配置

export function Chat() {
    const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
        api: '/api/chat',
        ...customConfig  // 业务层扩展配置
    });

    return (
        <div className="flex flex-col w-full max-w-xl mx-auto p-4 space-y-4">
            <div className="flex flex-col space-y-4 mb-4">
                {messages.map((message) => (
                    <div
                        key={message.id}
                        className={`p-3 rounded-lg ${message.role === 'user'
                                ? 'bg-blue-100 ml-auto'
                                : 'bg-gray-100'
                            }`}
                    >
                        {message.content}
                    </div>
                ))}
            </div>

            <form onSubmit={handleSubmit} className="flex space-x-2">
                <input
                    type="text"
                    value={input}
                    onChange={handleInputChange}
                    placeholder="Type your message..."
                    className="flex-1 p-2 border border-gray-300 rounded"
                    disabled={isLoading}
                />
                <button
                    type="submit"
                    className="px-4 py-2 bg-blue-500 text-white rounded"
                    disabled={isLoading}
                >
                    {isLoading ? 'Sending...' : 'Send'}
                </button>
            </form>
        </div>
    );
} 