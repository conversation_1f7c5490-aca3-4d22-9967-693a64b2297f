{"name": "@kit/cms", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/cms-types": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/keystatic": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/wordpress": "workspace:*", "@types/node": "^22.15.18", "@types/react": "^18.3.21", "react": "18.3.1"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}