import { MetaFunction } from '@remix-run/react';

import { SitePageHeader } from '~/routes/_marketing/_components/site-page-header';

export const meta: MetaFunction = () => {
  return [
    {
      title: "Mother's Day Experiences - Create Memorable Moments",
      description: "Plan special experiences and activities to make Mother's Day unforgettable for your mom.",
    },
  ];
};

export default function ExperiencesPage() {
  return (
    <div className="flex flex-col space-y-12">
      <SitePageHeader 
        title="Mother's Day Experiences" 
        subtitle="Plan special activities and moments to make Mother's Day unforgettable"
      />

      <div className="container mx-auto pb-16">
        <div className="bg-pink-50 dark:bg-purple-900/20 p-8 rounded-2xl text-center">
          <h2 className="text-2xl font-bold mb-4">Coming Soon!</h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            We're creating detailed guides and planners to help you organize the perfect 
            Mother's Day experience. From breakfast in bed to special outings, our AI will 
            help you plan activities tailored to your mom's preferences. Check back soon!
          </p>
        </div>
      </div>
    </div>
  );
}
