import { Link, MetaFunction, useLoaderData } from '@remix-run/react';
import type { LoaderFunctionArgs } from '@remix-run/server-runtime';
import { ArrowRight, ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { SitePageHeader } from '~/routes/_marketing/_components/site-page-header';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { t } = await createI18nServerInstance(request);

  const faqItems = [
    {
      question: `What is <PERSON><PERSON><PERSON>?`,
      answer: `ZenKira is an innovative sleep assistance application that automatically generates personalized audio content based on your real-time physiological state. Unlike standard sleep audio content, ZenKira uses advanced AI technology to analyze your heart rate, breathing, and other physiological data to adjust audio characteristics in real-time, providing a tailored sleep experience.`,
    },
    {
      question: `How does <PERSON><PERSON><PERSON> work?`,
      answer: `ZenKira uses a closed-loop feedback system that continuously monitors your physiological indicators like heart rate to assess your sleep state. Our AI-driven technology analyzes your sleep patterns and physiological data, then dynamically adjusts audio frequency, rhythm, and complexity based on your current sleep stage. The system learns and optimizes recommendations as you use it.`,
    },
    {
      question: `What results can I expect from using ZenKira?`,
      answer: `Based on our research, ZenKira users typically experience: 28-35% reduction in time to fall asleep, 40-45% fewer nighttime awakenings, 22-30% increase in deep sleep duration, 18-25% improvement in overall sleep efficiency, enhanced daytime energy and alertness, more stable mood, improved cognitive performance, and reduced stress levels.`,
    },
    {
      question: `Is ZenKira suitable for everyone?`,
      answer: `ZenKira is designed for various user groups including insomnia sufferers, stressed professionals, health enthusiasts, shift workers, and seniors. Unlike medication, ZenKira offers a non-addictive solution without negative side effects. If you have severe sleep disorders or medical conditions, we recommend consulting with a healthcare professional.`,
    },
    {
      question: `Do you offer a free trial?`,
      answer: `Yes, we offer a 14-day free trial so you can experience ZenKira's personalized sleep technology. You can cancel at any time during the trial period and you won't be charged.`,
    },
    {
      question: `How is ZenKira different from other sleep apps?`,
      answer: `Unlike most sleep apps that use pre-recorded audio content, ZenKira generates personalized audio in real-time based on your current physiological state. Our multi-sensor data fusion and machine learning algorithms recognize your unique response patterns to different audio characteristics. The system continuously learns your preferences and optimizes your sleep experience over time.`,
    },
    {
      question: `What devices is ZenKira compatible with?`,
      answer: `ZenKira is compatible with most modern smartphones and wearable devices that can track heart rate and other biometrics. For optimal results, we recommend using devices with accurate heart rate monitoring capabilities. Please check our compatibility page for a complete list of supported devices.`,
    },
    {
      question: `How do I access my sleep data and analysis?`,
      answer: `ZenKira provides comprehensive sleep quality analysis and improvement suggestions through our app dashboard. You can view trends over time, see detailed breakdowns of your sleep cycles, and receive personalized recommendations to improve your sleep habits.`,
    },
  ];

  return {
    title: t('marketing:faq'),
    faqItems,
  };
};

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    {
      title: data?.title,
    },
  ];
};

export default function FAQPage() {
  const { faqItems } = useLoaderData<typeof loader>();
  const { t } = useTranslation();

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqItems.map((item) => {
      return {
        '@type': 'Question',
        name: item.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: item.answer,
        },
      };
    }),
  };

  return (
    <>
      <script
        key={'ld:json'}
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className={'flex flex-col space-y-4 xl:space-y-8'}>
        <SitePageHeader
          title={t('marketing:faq')}
          subtitle={t('marketing:faqSubtitle')}
        />

        <div className={'container flex flex-col space-y-8 pb-16'}>
          <div className="flex w-full max-w-xl flex-col">
            {faqItems.map((item, index) => {
              return <FaqItem key={index} item={item} />;
            })}
          </div>

          <div>
            <Button asChild variant={'outline'}>
              <Link to={'/contact'}>
                <span>
                  <Trans i18nKey={'marketing:contactFaq'} />
                </span>

                <ArrowRight className={'ml-2 w-4'} />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}

function FaqItem({
  item,
}: React.PropsWithChildren<{
  item: {
    question: string;
    answer: string;
  };
}>) {
  return (
    <details className={'group border-b px-2 py-4 last:border-b-transparent'}>
      <summary
        className={
          'flex items-center justify-between hover:cursor-pointer hover:underline'
        }
      >
        <h2
          className={
            'hover:underline-none cursor-pointer font-sans font-medium'
          }
        >
          <Trans i18nKey={item.question} defaults={item.question} />
        </h2>

        <div>
          <ChevronDown
            className={'h-5 transition duration-300 group-open:-rotate-180'}
          />
        </div>
      </summary>

      <div className={'text-muted-foreground flex flex-col space-y-2 py-1'}>
        <Trans i18nKey={item.answer} defaults={item.answer} />
      </div>
    </details>
  );
}
