import { AudioCard } from "./AudioCard"
import type { Audio } from "../route"

interface AudioGridProps {
    audios: Audio[]
    onSelect: (audio: Audio) => void
    compact?: boolean
}

export function AudioGrid({ audios, onSelect, compact = false }: AudioGridProps) {
    if (!audios.length) {
        return (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                <p>No audio found matching your criteria</p>
            </div>
        )
    }

    return (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {audios.map((audio) => (
                <div key={audio.id} className="w-full">
                    <div className="w-48 mx-auto">
                        <AudioCard audio={audio} onSelect={() => onSelect(audio)} compact={compact} />
                    </div>
                </div>
            ))}
        </div>
    )
}