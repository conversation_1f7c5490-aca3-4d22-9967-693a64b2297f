{"name": "@kit/ai", "private": true, "version": "0.1.0", "license": "MIT", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit", "test:ai": "tsx src/debug/test-runner.ts"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/axios": "^0.14.4", "@types/node": "^22.15.18", "@types/uuid": "^10.0.0", "dotenv": "^16.5.0", "tsx": "^4.19.4", "uuid": "^11.1.0", "vitest": "^3.1.3"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}, "dependencies": {"axios": "^1.9.0", "openai": "^4.98.0", "zod": "^3.24.4"}}