import { ChevronLeft, ChevronRight } from "lucide-react"
import { useRef } from "react"
import { AudioCard } from "./AudioCard"
import type { Audio } from "../route"

interface Collection {
    id: string
    title: string
    audios: Audio[]
}

interface ThemeCollectionProps {
    collection: Collection
    onSelect: (audio: Audio) => void
}

export function ThemeCollection({ collection, onSelect }: ThemeCollectionProps) {
    const scrollContainerRef = useRef<HTMLDivElement>(null)

    const scrollLeft = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollBy({ left: -300, behavior: "smooth" })
        }
    }

    const scrollRight = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollBy({ left: 300, behavior: "smooth" })
        }
    }

    return (
        <div className="relative">
            <div
                ref={scrollContainerRef}
                className="flex overflow-x-auto pb-4 hide-scrollbar"
                style={{ scrollbarWidth: "none" }}
            >
                <div className="flex space-x-4">
                    {collection.audios.map((audio) => (
                        <div key={audio.id} className="w-48 flex-shrink-0">
                            <AudioCard audio={audio} onSelect={() => onSelect(audio)} compact />
                        </div>
                    ))}
                </div>
            </div>

            {/* Scroll control buttons */}
            <button
                onClick={scrollLeft}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-600 dark:text-gray-300 z-10"
            >
                <ChevronLeft size={20} />
            </button>

            <button
                onClick={scrollRight}
                className="absolute right-0 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-600 dark:text-gray-300 z-10"
            >
                <ChevronRight size={20} />
            </button>
        </div>
    )
}
