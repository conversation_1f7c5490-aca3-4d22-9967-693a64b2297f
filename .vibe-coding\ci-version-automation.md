# Automated Version Management with GitHub Actions

**Author:** Backend Engineering Team  
**Date:** March 24, 2025

---

## Overview

This document describes the automated version management system implemented in our GitHub Actions CI pipeline. The system automatically generates version numbers based on Git tags and commit history, eliminating the need for manual version updates.

---

## Version Generation Strategy

Our version management follows semantic versioning principles with the following format:

- **Production releases:** `MAJOR.MINOR.PATCH`
- **Development builds:** `MAJOR.MINOR.PATCH-branch-name.commit-count`

### Version Components

1. **Version Code:** A numeric representation calculated as `MAJOR * 10000 + MINOR * 100 + PATCH + COMMITS`
2. **Version Name:** A human-readable string following semantic versioning

---

## GitHub Actions Implementation

The version generation is implemented as a step in our GitHub Actions workflow:

```yaml
- name: Generate Version Information
  id: version
  run: |
    # Debug: List all tags
    echo "All tags:"
    git tag -l

    # Get the latest tag or use v0.0.0 if no tags exist
    LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
    echo "Latest tag: $LATEST_TAG"

    # Extract version components
    VERSION=${LATEST_TAG#v}
    IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"

    # Count commits since the latest tag
    COMMITS=$(git rev-list --count $LATEST_TAG..HEAD 2>/dev/null || echo "0")

    # Generate version code (MAJOR * 10000 + MINOR * 100 + PATCH + COMMITS)
    VERSION_CODE=$((MAJOR * 10000 + MINOR * 100 + PATCH + COMMITS))

    # Generate version name
    if [ "$GITHUB_REF_NAME" = "main" ]; then
        # For main branch, use the tag version with commit count if any
        if [ "$COMMITS" -gt "0" ]; then
            VERSION_NAME="$MAJOR.$MINOR.$((PATCH + COMMITS))"
        else
            VERSION_NAME="$MAJOR.$MINOR.$PATCH"
        fi
    else
        # For other branches, append branch name and commit count
        BRANCH_NAME=${GITHUB_REF_NAME//\//-}
        VERSION_NAME="$MAJOR.$MINOR.$PATCH-$BRANCH_NAME.$COMMITS"
    fi

    echo "VERSION_CODE=$VERSION_CODE" >> $GITHUB_ENV
    echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV

    echo "Generated version code: $VERSION_CODE"
    echo "Generated version name: $VERSION_NAME"

    # Debug: Print all environment variables
    echo "All environment variables:"
    env
```

**Important:** This step requires the checkout action with `fetch-depth: 0` to access all Git history:

```yaml
- uses: actions/checkout@v4
  with:
    fetch-depth: 0  # Fetch all history and tags
```

---

## How It Works

1. **Tag Detection:** The script finds the latest Git tag (e.g., `v1.2.3`) or defaults to `v0.0.0` if none exists
2. **Version Parsing:** Extracts the major, minor, and patch numbers from the tag
3. **Commit Counting:** Counts the number of commits since the latest tag
4. **Version Calculation:**
   - **Version Code:** Calculated as `MAJOR * 10000 + MINOR * 100 + PATCH + COMMITS`
   - **Version Name:** Generated based on branch and commit information

---

## Version Naming Rules

1. **Main Branch:**
   - If no new commits since the latest tag: `MAJOR.MINOR.PATCH`
   - If new commits exist: `MAJOR.MINOR.(PATCH+COMMITS)`

2. **Other Branches:**
   - Format: `MAJOR.MINOR.PATCH-branch-name.commit-count`
   - Example: `1.2.3-feature-login.5` (5 commits on the feature-login branch since tag v1.2.3)

---

## Usage in Workflows

The generated version information is available as environment variables:

- `${{ env.VERSION_CODE }}` - Numeric version code
- `${{ env.VERSION_NAME }}` - String version name

These can be used in subsequent steps for:

- Setting application version in build processes
- Tagging Docker images
- Creating release artifacts
- Including in application metadata

---

## Tagging Strategy

To effectively use this system:

1. Create a new tag (e.g., `v1.2.3`) for each release
2. Push the tag to the repository: `git tag v1.2.3 && git push origin v1.2.3`
3. The CI system will automatically calculate the next version based on this tag

---

## Benefits

- **Automation:** No manual version updates required
- **Consistency:** Standardized version format across all builds
- **Traceability:** Versions directly tied to Git history
- **Flexibility:** Works with any branching strategy

---

## Example Scenarios

### Scenario 1: Release from Main Branch

1. Latest tag: `v1.2.3`
2. No new commits
3. Generated version: `1.2.3` (code: `10203`)

### Scenario 2: Development on Main Branch

1. Latest tag: `v1.2.3`
2. 5 new commits
3. Generated version: `1.2.8` (code: `10208`)

### Scenario 3: Feature Branch

1. Latest tag: `v1.2.3`
2. Branch: `feature/user-auth`
3. 7 new commits
4. Generated version: `1.2.3-feature-user-auth.7` (code: `10210`)

---

## Troubleshooting

If version generation fails:

1. Ensure the checkout action has `fetch-depth: 0`
2. Verify Git tags exist and are accessible
3. Check the workflow logs for debugging information
4. Ensure the script has proper permissions to run

---

## Further Reading

- [Semantic Versioning](https://semver.org/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Git Tagging](https://git-scm.com/book/en/v2/Git-Basics-Tagging)
