import type { Workflow, WorkflowContext } from '../types';
import { getConfig } from '../../config/index';
import { AIError } from '../utils/error-handling';

/**
 * Execute a workflow with the given initial context
 * @param workflow The workflow to execute
 * @param initialContext The initial context for the workflow
 * @returns The final workflow context after execution
 */
export class WorkflowEngine {
    private config: any;

    constructor() {
        this.config = getConfig();
    }

    async execute(workflow: Workflow, initialContext: WorkflowContext): Promise<WorkflowContext> {
        console.log(`Starting workflow: ${workflow.name}`);
        
        // 检查此工作流是否需要特定的 API 密钥
        const requiredApis = this.getRequiredApisForWorkflow(workflow.name);
        if (requiredApis.length > 0) {
            this.validateApiKeys(requiredApis);
        }
        
        try {
            const startTime = Date.now();
            const result = await workflow.execute(initialContext);
            const endTime = Date.now();
            
            console.log(`Workflow completed: ${workflow.name} in ${(endTime - startTime)/1000}s`);
            return result;
        } catch (error) {
            console.error(`Error in workflow ${workflow.name}:`, error);
            throw error;
        }
    }
    
    private getRequiredApisForWorkflow(workflowName: string): string[] {
        // 根据工作流名称返回所需的 API 列表
        const workflows: Record<string, string[]> = {
            'image-generation': ['xai'],
            'chat-completion': ['openai', 'deepseek'],
            // 其他工作流可以根据需要添加
        };
        
        return workflows[workflowName] || [];
    }

    private validateApiKeys(requiredApis: string[]): void {
        const missingApis = requiredApis.filter(api => {
            return !this.config[api]?.apiKey;
        });
        
        if (missingApis.length > 0) {
            console.warn(`警告: 工作流缺少所需的 API 密钥: ${missingApis.join(', ')}`);
            // 不立即抛出错误，让具体服务在调用时处理
        }
    }
}

export const workflowEngine = new WorkflowEngine();
