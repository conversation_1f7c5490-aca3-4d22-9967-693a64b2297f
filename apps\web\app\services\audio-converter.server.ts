import { createId } from '@paralleldrive/cuid2';
// import { r2Storage } from './r2-storage.server';
import { saveAudioToCache } from "~/routes/audio.$id";

// 音频转换的配置
interface AudioConversionConfig {
  serverUrl?: string;
  soundfont?: string;
  sampleRate?: number;
  format?: string;
  fileExtension?: string; // 新增: 允许设置输出文件扩展名
}

/**
 * 音频转换服务
 * 负责将JSON格式的音频描述转换为实际的音频文件
 */
export class AudioConverter {
  private serverUrl: string;
  private soundfont: string;
  private sampleRate: number;
  private format: string;
  private fileExtension: string;

  constructor({
    serverUrl = process.env.AUDIO_CONVERSION_SERVER_URL || 'http://165.154.29.76:9000/convert',
    soundfont = process.env.AUDIO_CONVERSION_SOUNDFONT || '/usr/share/soundfonts/GeneralUser-GS.sf2',
    sampleRate = process.env.AUDIO_CONVERSION_SAMPLE_RATE ? parseInt(process.env.AUDIO_CONVERSION_SAMPLE_RATE) : 44100,
    format = process.env.AUDIO_CONVERSION_FORMAT || 'aac',
    fileExtension = process.env.AUDIO_FILE_EXTENSION || 'aac' // 默认文件扩展名为aac，不管实际内容
  }: AudioConversionConfig = {}) {
    this.serverUrl = serverUrl;
    this.soundfont = soundfont;
    this.sampleRate = sampleRate;
    this.format = format;
    this.fileExtension = fileExtension;
  }

  /**
   * 将JSON转换为音频
   * @param jsonData 音频的JSON描述
   * @returns 包含音频URL和文件信息的对象
   */
  async convertJsonToAudio(jsonData: string): Promise<{ audioUrl: string, filename: string, contentType: string }> {
    console.log(`Converting JSON to audio, JSON length: ${jsonData.length} characters`);
    
    // 构建请求URL
    const url = `${this.serverUrl}?format=${this.format}&sample_rate=${this.sampleRate}&soundfont=${encodeURIComponent(this.soundfont)}`;
    console.log(`[DEBUG] 请求转换服务的完整URL: ${url}`);
    
    try {
      // 验证JSON数据有效性
      let jsonObject = JSON.parse(jsonData);
      console.log(`[DEBUG] JSON数据解析成功，包含字段: ${Object.keys(jsonObject).join(', ')}`);
      
      // 发送请求到转换服务
      console.log(`[DEBUG] 发送请求至转换服务...`);
      const formData = new FormData();
      formData.append('json_data', jsonData); // 直接使用原始JSON字符串
      
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
      });

      console.log(`[DEBUG] 收到响应状态码: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Audio conversion failed with status: ${response.status}, details: ${errorText}`);
      }

      // 获取音频数据
      const audioBuffer = await response.arrayBuffer();
      const audioData = Buffer.from(audioBuffer);
      console.log(`Received audio data, size: ${audioData.length} bytes`);
      
      // 从响应头中获取服务器返回的内容类型
      const contentType = response.headers.get('content-type') || 'audio/wav';
      console.log(`[DEBUG] 接收到的内容类型: ${contentType}`);
      
      // 生成一个唯一的文件名
      const fileId = createId();
      const filename = `audio_${fileId}.${this.fileExtension}`;
      
      // 对于所有大小的文件，都使用缓存机制，避免数据URL
      console.log(`[DEBUG] 使用音频缓存机制`);
      const audioId = saveAudioToCache(audioData, contentType);
      const audioUrl = `/audio/${audioId}`;
      
      return {
        audioUrl,
        filename,
        contentType
      };
    } catch (error) {
      console.error('[DEBUG] 音频转换过程中出现错误:', error);
      console.error('[DEBUG] 错误类型:', error instanceof Error ? error.name : typeof error);
      console.error('[DEBUG] 错误消息:', error instanceof Error ? error.message : String(error));
      console.error('[DEBUG] 错误堆栈:', error instanceof Error ? error.stack : '无堆栈信息');
      
      // 修复: 出错时重新抛出异常，而不是返回undefined
      throw error;
    }
  }

  /**
   * 根据内容类型和文件名确定正确的文件扩展名
   */
  private getExtensionFromContentType(contentType: string, filename: string): string {
    // 首先尝试从文件名获取扩展名
    const fileExtMatch = /\.([^.]+)$/.exec(filename);
    if (fileExtMatch && fileExtMatch[1]) {
      return fileExtMatch[1];
    }
    
    // 然后从内容类型确定扩展名
    switch (contentType.toLowerCase()) {
      case 'audio/aac':
        return 'aac';
      case 'audio/mpeg':
        return 'mp3';
      case 'audio/wav':
      case 'audio/wave':
      case 'audio/x-wav':
        return 'wav';
      case 'audio/ogg':
        return 'ogg';
      default:
        // 默认使用我们请求的格式
        return this.format === 'aac' ? 'aac' : 'wav';
    }
  }

  /**
   * 尝试使用MIDI文件进行转换（作为后备方案）
   * @param jsonData 音频的JSON描述
   * @returns 包含音频URL的对象
   */
  async convertMidiToAudio(midiBuffer: Buffer): Promise<{ audioUrl: string, filename: string }> {
    console.log(`Converting MIDI to audio, buffer size: ${midiBuffer.length} bytes`);
    
    // 构建请求URL
    const url = `${this.serverUrl}?format=${this.format}&sample_rate=${this.sampleRate}&soundfont=${encodeURIComponent(this.soundfont)}`;
    
    try {
      // 发送请求到转换服务
      console.log(`[DEBUG] 尝试使用MIDI文件方式发送请求...`);
      const formData = new FormData();
      const blob = new Blob([midiBuffer], { type: 'audio/midi' });
      formData.append('midi_file', blob, 'music.mid');
      
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
      });

      console.log(`[DEBUG] 收到响应状态码: ${response.status}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`MIDI conversion failed with status: ${response.status}, details: ${errorText}`);
      }

      // 获取音频数据
      const audioBuffer = await response.arrayBuffer();
      const audioData = Buffer.from(audioBuffer);
      
      // 生成一个临时的文件名和URL
      const extension = 'aac';
      const fileId = createId();
      const filename = `audio_${fileId}.${extension}`;
      const audioUrl = `/temp/${filename}`;
      
      return {
        audioUrl,
        filename
      };
    } catch (error) {
      console.error('[DEBUG] MIDI转换过程中出现错误:', error);
      throw error;
    }
  }

  // 增加一个转换方法
  async convertWavToAac(wavBuffer: Buffer): Promise<Buffer> {
    // 这里需要集成音频转换库，如ffmpeg-wasm
    console.log("[DEBUG] 尝试将WAV转换为AAC...");
    
    // 这只是一个示例，实际实现需要依赖库
    // const result = await ffmpeg.convert({
    //   input: wavBuffer,
    //   inputFormat: 'wav',
    //   outputFormat: 'aac'
    // });
    
    // 实际项目中，如果不能在服务器端实现转换，可以考虑:
    // 1. 使用客户端JavaScript库处理转换
    // 2. 调用另一个转换服务
    // 3. 使用Web Audio API进行简单转换

    // 假设转换完成，返回结果
    return wavBuffer; // 实际应返回转换后的AAC buffer
  }
}

// 导出单例实例，方便在应用中使用
export const audioConverter = new AudioConverter(); 