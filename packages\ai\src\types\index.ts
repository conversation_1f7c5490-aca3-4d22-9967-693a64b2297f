export type AIProvider = "openai" | "anthropic" | "deepseek" | "groq" | "siliconflow" | "jina" | "xai";

export interface AIResponse {
    text: string;
    provider: AIProvider;
    metadata?: Record<string, unknown>;
}

export interface AIRequest {
    prompt: string;
    provider?: AIProvider;
    options?: Record<string, unknown>;
}

export interface ChatMessage {
    role: "system" | "user" | "assistant" | "function" | "tool";
    content: string;
    name?: string;
    id?: string;
}

export interface Task {
    id: string;
    type: string;
    status: TaskStatus;
    params: Record<string, unknown>;
    result?: unknown;
    error?: string;
    createdAt: Date;
    updatedAt: Date;
}

export type TaskStatus = "pending" | "running" | "completed" | "failed";

export * from './workflow';
