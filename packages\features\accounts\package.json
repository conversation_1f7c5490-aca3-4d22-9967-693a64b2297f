{"name": "@kit/accounts", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "exports": {"./personal-account-dropdown": "./src/components/personal-account-dropdown.tsx", "./account-selector": "./src/components/account-selector.tsx", "./personal-account-settings": "./src/components/personal-account-settings/index.ts", "./hooks/*": "./src/hooks/*.ts", "./api": "./src/server/api.ts", "./schema": "./src/schema/index.ts", "./actions": "./src/server/personal-account-actions.server.ts"}, "dependencies": {"nanoid": "^5.1.5"}, "devDependencies": {"@hookform/resolvers": "^3.10.0", "@kit/billing-gateway": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@radix-ui/react-icons": "^1.3.2", "@remix-run/react": "2.15.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "5.61.5", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "lucide-react": "^0.462.0", "next-themes": "0.4.3", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "sonner": "^1.7.4", "zod": "^3.24.4"}, "prettier": "@kit/prettier-config", "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}