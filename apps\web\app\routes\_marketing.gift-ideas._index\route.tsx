import { MetaFunction } from '@remix-run/react';

import { SitePageHeader } from '~/routes/_marketing/_components/site-page-header';

export const meta: MetaFunction = () => {
  return [
    {
      title: "Mother's Day Gift Ideas - Find the Perfect Gift for Mom",
      description: "Discover personalized gift ideas for Mother's Day that will make your mom feel special and loved.",
    },
  ];
};

export default function GiftIdeasPage() {
  return (
    <div className="flex flex-col space-y-12">
      <SitePageHeader 
        title="Mother's Day Gift Ideas" 
        subtitle="Discover personalized gift ideas that will make your mom feel special and loved"
      />

      <div className="container mx-auto pb-16">
        <div className="bg-pink-50 dark:bg-purple-900/20 p-8 rounded-2xl text-center">
          <h2 className="text-2xl font-bold mb-4">Coming Soon!</h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            We're working on curating the perfect collection of Mother's Day gift ideas. 
            Check back soon for personalized gift suggestions based on your mom's interests, 
            personality, and your budget.
          </p>
        </div>
      </div>
    </div>
  );
}
