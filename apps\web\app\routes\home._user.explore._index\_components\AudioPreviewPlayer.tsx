import { Form } from "@remix-run/react"
import { X, Play, Pause, Heart, Share2, Flag, Plus, Volume2 } from "lucide-react"
import { useState, useRef, useEffect } from "react"
import type { Audio } from "../route"

interface AudioPreviewPlayerProps {
    audio: Audio
    onClose: () => void
}

export function AudioPreviewPlayer({ audio, onClose }: AudioPreviewPlayerProps) {
    const [isPlaying, setIsPlaying] = useState(false)
    const [progress, setProgress] = useState(0)
    const [currentTime, setCurrentTime] = useState("0:00")
    const [volume, setVolume] = useState(80)

    const audioRef = useRef<HTMLAudioElement | null>(null)
    const progressIntervalRef = useRef<number | null>(null)

    // Audio playback simulation
    useEffect(() => {
        if (isPlaying) {
            progressIntervalRef.current = window.setInterval(() => {
                setProgress((prev) => {
                    if (prev >= 100) {
                        setIsPlaying(false)
                        clearInterval(progressIntervalRef.current!)
                        return 0
                    }

                    const totalSeconds = parseDuration(audio.duration)
                    const currentSeconds = Math.floor((prev / 100) * totalSeconds)
                    setCurrentTime(formatTime(currentSeconds))

                    return prev + 0.1
                })
            }, 100)
        } else if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current)
        }

        return () => {
            if (progressIntervalRef.current) {
                clearInterval(progressIntervalRef.current)
            }
        }
    }, [isPlaying, audio.duration])

    return (
        <div className="fixed inset-x-0 bottom-16 z-20 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg">
            <div className="container mx-auto p-4">
                <div className="flex items-center">
                    {/* Cover image */}
                    <div
                        className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 mr-4"
                        style={{
                            backgroundImage: `url(${audio.coverImage || "/placeholder.svg?height=64&width=64"})`,
                            backgroundSize: "cover",
                            backgroundPosition: "center",
                        }}
                    />

                    {/* Audio info and controls */}
                    <div className="flex-1">
                        <div className="flex justify-between items-start mb-1">
                            <div>
                                <h3 className="font-medium text-gray-900 dark:text-white">{audio.title}</h3>
                                {audio.description && (
                                    <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1">{audio.description}</p>
                                )}
                            </div>
                            <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                                <X size={18} className="text-gray-500 dark:text-gray-400" />
                            </button>
                        </div>

                        {/* Progress bar */}
                        <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400 w-8">{currentTime}</span>
                            <input
                                type="range"
                                min="0"
                                max="100"
                                value={progress}
                                onChange={(e) => {
                                    const newProgress = Number.parseFloat(e.target.value)
                                    setProgress(newProgress)
                                    const totalSeconds = parseDuration(audio.duration)
                                    const currentSeconds = Math.floor((newProgress / 100) * totalSeconds)
                                    setCurrentTime(formatTime(currentSeconds))
                                }}
                                className="flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded-full appearance-none cursor-pointer"
                                style={{
                                    background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${progress}%, #e5e7eb ${progress}%, #e5e7eb 100%)`,
                                }}
                            />
                            <span className="text-xs text-gray-500 dark:text-gray-400 w-8">{audio.duration}</span>
                        </div>
                    </div>
                </div>

                {/* Control buttons */}
                <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center space-x-2">
                        <button
                            onClick={() => setIsPlaying(!isPlaying)}
                            className="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center"
                        >
                            {isPlaying ? <Pause size={20} /> : <Play size={20} className="ml-1" />}
                        </button>

                        <div className="flex items-center space-x-1 ml-2">
                            <Volume2 size={18} className="text-gray-500 dark:text-gray-400" />
                            <input
                                type="range"
                                min="0"
                                max="100"
                                value={volume}
                                onChange={(e) => setVolume(Number.parseFloat(e.target.value))}
                                className="w-20 h-1 bg-gray-200 dark:bg-gray-700 rounded-full appearance-none cursor-pointer"
                                style={{
                                    background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${volume}%, #e5e7eb ${volume}%, #e5e7eb 100%)`,
                                }}
                            />
                        </div>
                    </div>

                    <div className="flex items-center space-x-4">
                        <Form method="post">
                            <input type="hidden" name="audioId" value={audio.id} />
                            <input type="hidden" name="intent" value="favorite" />
                            <button
                                type="submit"
                                className="p-2 rounded-full text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            >
                                <Heart size={20} />
                            </button>
                        </Form>

                        <button className="p-2 rounded-full text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                            <Share2 size={20} />
                        </button>

                        <button className="p-2 rounded-full text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                            <Flag size={20} />
                        </button>

                        <Form method="post">
                            <input type="hidden" name="audioId" value={audio.id} />
                            <input type="hidden" name="intent" value="add-to-library" />
                            <button
                                type="submit"
                                className="flex items-center space-x-1 px-3 py-1 rounded-full bg-primary text-white"
                            >
                                <Plus size={16} />
                                <span className="text-sm">Add to Library</span>
                            </button>
                        </Form>
                    </div>
                </div>
            </div>

            {/* Hidden audio element */}
            <audio ref={audioRef} src="" className="hidden" />
        </div>
    )
}

// Helper functions
function parseDuration(duration: string | undefined): number {
    if (!duration) return 0;
    
    const parts = duration.split(":")
    if (parts.length === 2) {
        return Number.parseInt(parts[0], 10) * 60 + Number.parseInt(parts[1], 10)
    }
    if (parts.length === 3) {
        return Number.parseInt(parts[0], 10) * 3600 + Number.parseInt(parts[1], 10) * 60 + Number.parseInt(parts[2], 10)
    }
    return 0
}

function formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
}