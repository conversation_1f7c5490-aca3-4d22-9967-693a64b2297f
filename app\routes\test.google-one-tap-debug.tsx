/**
 * Google One Tap Debug Page
 * 用于测试和调试Google One Tap配置
 */

import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useState, useEffect } from "react";
import { GoogleOneTap } from "~/components/auth/google-one-tap";
import UnifiedLayout from "~/components/layout/unified-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";

export const meta: MetaFunction = () => {
  return [
    { title: "Google One Tap Debug - AI SaaS Starter" },
    { name: "description", content: "Debug Google One Tap configuration" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  
  return json({
    googleClientId: process.env.GOOGLE_CLIENT_ID,
    oneTapEnabled: process.env.ONE_TAP_ENABLED,
    googleOneTapEnabled: process.env.GOOGLE_ONE_TAP_ENABLED,
    nodeEnv: process.env.NODE_ENV,
    currentUrl: url.href,
    origin: url.origin,
    hostname: url.hostname,
  });
}

export default function GoogleOneTapDebugPage() {
  const data = useLoaderData<typeof loader>();
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [googleApiLoaded, setGoogleApiLoaded] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    setDebugLogs(prev => [...prev, logMessage]);
  };

  useEffect(() => {
    addLog("页面加载完成");
    addLog(`Google Client ID: ${data.googleClientId ? "已配置" : "未配置"}`);
    addLog(`One Tap Enabled: ${data.oneTapEnabled}`);
    addLog(`Google One Tap Enabled: ${data.googleOneTapEnabled}`);
    addLog(`当前环境: ${data.nodeEnv}`);
    addLog(`当前URL: ${data.currentUrl}`);
    addLog(`Origin: ${data.origin}`);

    // 检查Google API是否已加载
    const checkGoogleApi = () => {
      if (window.google?.accounts?.id) {
        setGoogleApiLoaded(true);
        addLog("✅ Google Identity Services API 已加载");
      } else {
        addLog("❌ Google Identity Services API 未加载");
      }
    };

    checkGoogleApi();
    
    // 定期检查API状态
    const interval = setInterval(checkGoogleApi, 1000);
    
    return () => clearInterval(interval);
  }, [data]);

  const testManualInit = () => {
    addLog("🔧 手动测试Google One Tap初始化...");
    
    if (!data.googleClientId) {
      addLog("❌ 错误: Google Client ID 未配置");
      return;
    }

    if (!window.google?.accounts?.id) {
      addLog("❌ 错误: Google Identity Services API 未加载");
      return;
    }

    try {
      addLog("🔧 初始化Google One Tap...");
      
      window.google.accounts.id.initialize({
        client_id: data.googleClientId,
        callback: (response: any) => {
          addLog("✅ Google回调触发!");
          addLog(`凭证接收: ${response.credential ? "是" : "否"}`);
          
          if (response.credential) {
            addLog(`凭证长度: ${response.credential.length}`);
            // 设置cookie并重定向
            document.cookie = `g_credential=${response.credential}; Path=/; SameSite=Lax${window.location.protocol === "https:" ? "; Secure" : ""}`;
            addLog("✅ Cookie已设置，准备重定向...");
            // 注意：这里不实际重定向，只是测试
            addLog("🔄 测试模式：跳过重定向到 /auth/google/callback");
          }
        },
        auto_select: false,
        cancel_on_tap_outside: true,
        use_fedcm_for_prompt: false,
        context: "signin",
        ux_mode: "popup",
        itp_support: true,
      });

      addLog("✅ Google One Tap 初始化成功");
      
      // 显示提示
      window.google.accounts.id.prompt((notification: any) => {
        addLog("📋 提示通知接收");
        
        if (notification.isNotDisplayed?.()) {
          const reason = notification.getNotDisplayedReason?.();
          addLog(`❌ 提示未显示，原因: ${reason}`);
          
          if (reason === "unregistered_origin") {
            addLog("💡 建议: 检查Google Console中的授权域名配置");
          } else if (reason === "invalid_client") {
            addLog("💡 建议: 检查Google Client ID是否正确");
          }
        } else {
          addLog("✅ 提示已显示");
        }
      });
      
    } catch (error) {
      addLog(`❌ 初始化错误: ${error}`);
    }
  };

  const clearLogs = () => {
    setDebugLogs([]);
  };

  return (
    <UnifiedLayout showHeader={true} showFooter={false} showSidebar={false}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* 标题 */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Google One Tap 调试工具
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              测试和调试Google One Tap认证配置
            </p>
          </div>

          {/* 配置信息 */}
          <Card>
            <CardHeader>
              <CardTitle>配置信息</CardTitle>
              <CardDescription>当前环境变量和配置状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Google Client ID:</strong> 
                  <span className={data.googleClientId ? "text-green-600" : "text-red-600"}>
                    {data.googleClientId ? " ✅ 已配置" : " ❌ 未配置"}
                  </span>
                </div>
                <div>
                  <strong>One Tap Enabled:</strong> 
                  <span className={data.oneTapEnabled === "true" ? "text-green-600" : "text-red-600"}>
                    {data.oneTapEnabled}
                  </span>
                </div>
                <div>
                  <strong>Google One Tap Enabled:</strong> 
                  <span className={data.googleOneTapEnabled === "true" ? "text-green-600" : "text-red-600"}>
                    {data.googleOneTapEnabled}
                  </span>
                </div>
                <div>
                  <strong>环境:</strong> {data.nodeEnv}
                </div>
                <div>
                  <strong>当前域名:</strong> {data.hostname}
                </div>
                <div>
                  <strong>Google API状态:</strong> 
                  <span className={googleApiLoaded ? "text-green-600" : "text-red-600"}>
                    {googleApiLoaded ? " ✅ 已加载" : " ❌ 未加载"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 测试区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Google One Tap 组件测试 */}
            <Card>
              <CardHeader>
                <CardTitle>Google One Tap 组件</CardTitle>
                <CardDescription>自动弹出测试</CardDescription>
              </CardHeader>
              <CardContent>
                <GoogleOneTap 
                  clientId={data.googleClientId} 
                  enabled={!!data.googleClientId} 
                />
              </CardContent>
            </Card>

            {/* 手动测试 */}
            <Card>
              <CardHeader>
                <CardTitle>手动测试</CardTitle>
                <CardDescription>手动触发Google One Tap</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <button
                  onClick={testManualInit}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  手动测试 Google One Tap
                </button>
                <button
                  onClick={clearLogs}
                  className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  清除日志
                </button>
              </CardContent>
            </Card>
          </div>

          {/* 调试日志 */}
          <Card>
            <CardHeader>
              <CardTitle>调试日志</CardTitle>
              <CardDescription>实时调试信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
                {debugLogs.length === 0 ? (
                  <div className="text-gray-500">等待调试信息...</div>
                ) : (
                  debugLogs.map((log, index) => (
                    <div key={index} className="mb-1">
                      {log}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UnifiedLayout>
  );
}
