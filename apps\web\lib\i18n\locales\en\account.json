{"accountTabLabel": "Account <PERSON><PERSON>", "accountTabDescription": "Manage your account settings", "homePage": "Home", "billingTab": "Billing", "settingsTab": "Settings", "multiFactorAuth": "Multi-Factor Authentication", "multiFactorAuthDescription": "Set up Multi-Factor Authentication method to further secure your account", "updateProfileSuccess": "Profile successfully updated", "updateProfileError": "Encountered an error. Please try again", "updatePasswordSuccess": "Password update request successful", "updatePasswordSuccessMessage": "Your password has been successfully updated!", "updatePasswordError": "Encountered an error. Please try again", "updatePasswordLoading": "Updating password...", "updateProfileLoading": "Updating profile...", "name": "Your Name", "nameDescription": "Update your name to be displayed on your profile", "emailLabel": "Email Address", "accountImage": "Your Profile Picture", "accountImageDescription": "Please choose a photo to upload as your profile picture.", "profilePictureHeading": "Upload a Profile Picture", "profilePictureSubheading": "Choose a photo to upload as your profile picture.", "updateProfileSubmitLabel": "Update Profile", "updatePasswordCardTitle": "Update your Password", "updatePasswordCardDescription": "Update your password to keep your account secure.", "currentPassword": "Current Password", "newPassword": "New Password", "repeatPassword": "Repeat New Password", "repeatPasswordDescription": "Please repeat your new password to confirm it", "yourPassword": "Your Password", "updatePasswordSubmitLabel": "Update Password", "updateEmailCardTitle": "Update your Email", "updateEmailCardDescription": "Update your email address you use to login to your account", "newEmail": "Your New Email", "repeatEmail": "Repeat Email", "updateEmailSubmitLabel": "Update Email Address", "updateEmailSuccess": "Email update request successful", "updateEmailSuccessMessage": "We sent you an email to confirm your new email address. Please check your inbox and click on the link to confirm your new email address.", "updateEmailLoading": "Updating your email...", "updateEmailError": "Email not updated. Please try again", "passwordNotMatching": "Passwords do not match. Make sure you're using the correct password", "emailNotMatching": "Emails do not match. Make sure you're using the correct email", "passwordNotChanged": "Your password has not changed", "emailsNotMatching": "Emails do not match. Make sure you're using the correct email", "cannotUpdatePassword": "You cannot update your password because your account is not linked to any.", "setupMfaButtonLabel": "Setup a new Factor", "multiFactorSetupErrorHeading": "Setup Failed", "multiFactorSetupErrorDescription": "Sorry, there was an error while setting up your factor. Please try again.", "multiFactorAuthHeading": "Secure your account with Multi-Factor Authentication", "multiFactorModalHeading": "Use your authenticator app to scan the QR code below. Then enter the code generated.", "factorNameLabel": "A memorable name to identify this factor", "factorNameHint": "Use an easy-to-remember name to easily identify this factor in the future. Ex. iPhone 14", "factorNameSubmitLabel": "Set factor name", "unenrollTooltip": "Unenroll this factor", "unenrollingFactor": "Unenrolling factor...", "unenrollFactorSuccess": "Factor successfully unenrolled", "unenrollFactorError": "Unenrolling factor failed", "factorsListError": "Error loading factors list", "factorsListErrorDescription": "Sorry, we couldn't load the factors list. Please try again.", "factorName": "Factor Name", "factorType": "Type", "factorStatus": "Status", "mfaEnabledSuccessTitle": "Multi-Factor authentication is enabled", "mfaEnabledSuccessDescription": "Congratulations! You have successfully enrolled in the multi factor authentication process. You will now be able to access your account with a combination of your password and an authentication code sent to your phone number.", "verificationCode": "Verification Code", "addEmailAddress": "Add Email address", "verifyActivationCodeDescription": "Enter the verification code generated by your authenticator app", "loadingFactors": "Loading factors...", "enableMfaFactor": "Enable Factor", "disableMfaFactor": "Disable Factor", "qrCodeErrorHeading": "QR Code Error", "qrCodeErrorDescription": "Sorry, we weren't able to generate the QR code", "multiFactorSetupSuccess": "Factor successfully enrolled", "submitVerificationCode": "Submit Verification Code", "mfaEnabledSuccessAlert": "Multi-Factor authentication is enabled", "verifyingCode": "Verifying code...", "invalidVerificationCodeHeading": "Invalid Verification Code", "invalidVerificationCodeDescription": "The verification code you entered is invalid. Please try again.", "unenrollFactorModalHeading": "Unenroll Factor", "unenrollFactorModalDescription": "You're about to unenroll this factor. You will not be able to use it to login to your account.", "unenrollFactorModalBody": "You're about to unenroll this factor. You will not be able to use it to login to your account.", "unenrollFactorModalButtonLabel": "Yes, unenroll factor", "selectFactor": "Choose a factor to verify your identity", "disableMfa": "Disable Multi-Factor Authentication", "disableMfaButtonLabel": "Disable MFA", "confirmDisableMfaButtonLabel": "Yes, disable MFA", "disablingMfa": "Disabling Multi-Factor Authentication. Please wait...", "disableMfaSuccess": "Multi-Factor Authentication successfully disabled", "disableMfaError": "Sorry, we encountered an error. MFA has not been disabled.", "sendingEmailVerificationLink": "Sending Email...", "sendEmailVerificationLinkSuccess": "Verification link successfully sent", "sendEmailVerificationLinkError": "Sorry, we weren't able to send you the email", "sendVerificationLinkSubmitLabel": "Send Verification Link", "sendVerificationLinkSuccessLabel": "Email sent! Check your Inbox", "verifyEmailAlertHeading": "Please verify your email to enable MFA", "verificationLinkAlertDescription": "Your email is not yet verified. Please verify your email to be able to set up Multi-Factor Authentication.", "authFactorName": "Factor Name (optional)", "authFactorNameHint": "Assign a name that helps you remember the phone number used", "loadingUser": "Loading user details. Please wait...", "linkPhoneNumber": "Link Phone Number", "dangerZone": "Danger Zone", "dangerZoneDescription": "Some actions cannot be undone. Please be careful.", "deleteAccount": "Delete your Account", "deletingAccount": "Deleting account. Please wait...", "deleteAccountDescription": "This will delete your account and the accounts you own. Furthermore, we will immediately cancel any active subscriptions. This action cannot be undone.", "deleteProfileConfirmationInputLabel": "Type DELETE to confirm", "deleteAccountErrorHeading": "Sorry, we couldn't delete your account", "needsReauthentication": "Reauthentication Required", "needsReauthenticationDescription": "You need to reauthenticate to change your password. Please sign out and sign in again to change your password.", "language": "Language", "languageDescription": "Choose your preferred language", "noTeamsYet": "You don't have any teams yet.", "createTeam": "Create a team to get started.", "createTeamButtonLabel": "Create a Team"}