# 认证与授权系统文档

## 1. 系统概述

本项目的认证与授权系统基于 Supabase Auth 构建，提供了完整的用户认证流程和基于角色的访问控制（RBAC）。系统设计注重安全性、可扩展性和用户体验，适用于多租户 SaaS 应用的复杂场景。

## 2. 认证系统

### 2.1 认证方式

系统支持多种认证方式，满足不同用户的需求和安全要求：

#### 2.1.1 电子邮件/密码认证

最基本的认证方式，支持用户通过电子邮件和密码进行注册和登录。

**主要功能**：
- 用户注册（`useSignUpWithEmailAndPassword`）
- 用户登录（`useSignInWithEmailPassword`）
- 邮箱验证
- 密码强度验证

**实现组件**：
- `PasswordSignUpForm` - 注册表单
- `PasswordSignInForm` - 登录表单

**流程**：
1. 用户填写电子邮件和密码
2. 系统验证输入格式和密码强度
3. 创建用户账户或验证登录信息
4. 发送验证邮件（注册时）
5. 完成认证并创建会话

#### 2.1.2 社交登录（可扩展）

支持通过第三方身份提供商进行身份验证，如 Google、Facebook、GitHub 等。

**主要功能**：
- 第三方身份提供商集成
- OAuth 2.0 认证流程
- 用户资料同步

**实现组件**：
- `OAuthProviders` - 提供多种社交登录选项
- `AuthProviderButton` - 社交登录按钮

**流程**：
1. 用户选择第三方身份提供商
2. 系统重定向到提供商的认证页面
3. 用户在提供商页面授权
4. 系统接收认证回调并创建/更新用户账户
5. 建立用户会话

#### 2.1.3 无密码登录（魔法链接）

允许用户通过发送到电子邮件的一次性链接登录，无需记住密码。

**主要功能**：
- 生成安全的一次性链接
- 验证链接有效性
- 建立用户会话

**实现组件**：
- `MagicLinkAuthContainer` - 无密码登录的容器组件

**流程**：
1. 用户提供电子邮件地址
2. 系统生成一次性访问链接并发送到用户邮箱
3. 用户点击链接
4. 系统验证链接并自动登录用户

### 2.2 双因素认证（2FA）

增强安全性的第二层验证机制，要求用户在主要认证方式之外提供第二种身份证明。

**主要功能**：
- TOTP（基于时间的一次性密码）支持
- 验证码输入和验证
- 因素管理（添加、删除）

**实现组件**：
- `MultiFactorChallengeContainer` - 多因素认证挑战界面
- `FactorsListContainer` - 认证因素管理

**流程**：
1. 用户完成主要认证（如密码登录）
2. 系统检测到已启用双因素认证
3. 提示用户输入验证码
4. 验证码验证成功后完成登录流程

### 2.3 会话管理

处理用户登录状态、会话持久化和安全退出等功能。

**主要功能**：
- 会话创建和存储
- 会话验证和刷新
- 会话过期和安全退出

**关键钩子**：
- `useAuthChangeListener` - 监听认证状态变化
- `useSignOut` - 处理用户退出

**实现方式**：
- 基于 JWT 的会话管理
- 自动会话刷新
- 会话状态同步（多标签页）

### 2.4 密码重置流程

允许用户在忘记密码时重新设置密码的安全流程。

**主要功能**：
- 密码重置请求
- 重置链接验证
- 新密码设置

**实现组件**：
- `PasswordResetRequestContainer` - 密码重置请求界面
- `UpdatePasswordForm` - 更新密码表单

**流程**：
1. 用户请求密码重置
2. 系统生成一次性密码重置链接并发送至用户邮箱
3. 用户点击链接访问密码重置页面
4. 用户设置新密码
5. 系统更新密码并自动登录用户

## 3. 授权系统

### 3.1 基于角色的访问控制（RBAC）

系统采用基于角色的访问控制模型，通过分配角色和权限来管理用户对资源的访问权限。

#### 3.1.1 数据库级别的 RBAC

在数据库层面实现的权限控制，确保数据安全性。

**主要数据表**：
- `roles` - 定义系统中的角色
  ```sql
  roles: {
    hierarchy_level: number;  // 角色层级，用于确定角色之间的关系
    name: string;             // 角色名称，如"owner"、"admin"、"member"
  }
  ```

- `role_permissions` - 定义角色与权限的关联
  ```sql
  role_permissions: {
    id: number;
    permission: app_permissions;  // 权限类型
    role: string;                 // 关联的角色
  }
  ```

- `accounts_memberships` - 用户与账户的关联，包含角色信息
  ```sql
  accounts_memberships: {
    account_id: string;
    user_id: string;
    account_role: string;  // 用户在账户中的角色
  }
  ```

**权限类型**：系统定义了以下核心权限类型（`app_permissions`）：
```typescript
app_permissions:
  | 'roles.manage'     // 管理角色和权限
  | 'billing.manage'   // 管理账单和订阅
  | 'settings.manage'  // 管理账户设置
  | 'members.manage'   // 管理团队成员
  | 'invites.manage'   // 管理邀请
```

#### 3.1.2 应用级别的 RBAC

在应用代码层面实现的权限控制，确保 UI 和功能访问安全。

**权限检查实现**：
- 数据库函数 `has_permission` - 检查用户是否具有特定权限
  ```typescript
  async hasPermission(params: {
    accountId: string;
    userId: string;
    permission: Database['public']['Enums']['app_permissions'];
  }) {
    const { data, error } = await this.client.rpc('has_permission', {
      account_id: params.accountId,
      user_id: params.userId,
      permission_name: params.permission,
    });

    if (error) {
      throw error;
    }

    return data;
  }
  ```

**权限控制应用**：
- 路由访问控制 - 基于用户角色限制页面访问
- UI 元素条件渲染 - 基于用户权限显示或隐藏特定功能
- API 端点保护 - 验证请求者是否有权执行操作

### 3.2 多租户权限模型

系统支持多租户模式，不同租户（账户）之间的数据和权限完全隔离。

**关键概念**：
- **个人账户** - 单用户账户，用户拥有所有权限
- **团队账户** - 多用户协作账户，不同用户有不同角色和权限

**权限继承**：
- 角色具有层级结构（`hierarchy_level`）
- 高级角色自动继承低级角色的所有权限

**权限隔离**：
- 所有权限检查都包含 `accountId` 参数
- 用户只能访问其所属账户的资源

## 4. 认证与授权流程示例

### 4.1 新用户注册与加入团队

1. 用户通过邮箱/密码或社交账号注册
2. 系统创建个人账户并分配"owner"角色
3. 如果用户通过邀请链接注册，系统将其添加到相应团队账户
4. 根据邀请中指定的角色分配相应权限

### 4.2 权限验证示例

**查看团队成员列表**：
1. 用户请求访问团队成员页面
2. 系统检查用户对该账户的 `members.manage` 权限
3. 如有权限，显示成员列表；否则显示权限不足提示

**管理账单订阅**：
1. 用户尝试更改订阅计划
2. 系统验证用户对该账户的 `billing.manage` 权限
3. 如有权限，允许操作；否则拒绝请求

## 5. 安全考量

### 5.1 密码安全

- 密码以加密方式存储（由 Supabase Auth 处理）
- 实施密码强度要求
- 定期提醒用户更新密码

### 5.2 会话安全

- 使用短期 JWT 令牌
- 实施自动会话超时
- 提供会话管理功能（查看活动会话，远程登出）

### 5.3 API 安全

- 所有敏感 API 端点都需要认证
- 实施速率限制防止暴力攻击
- 使用 HTTPS 加密所有通信

## 6. 最佳实践

### 6.1 开发建议

- 使用现有的权限检查函数，避免重新实现权限逻辑
- 前端组件应使用条件渲染而非隐藏敏感 UI 元素
- 所有权限逻辑必须在前端和后端同时实现

### 6.2 权限设计原则

- 遵循最小权限原则
- 创建明确的角色边界
- 定期审核角色和权限分配

## 7. 系统扩展

### 7.1 添加新角色

1. 在 `roles` 表中添加新角色
2. 设置适当的 `hierarchy_level`
3. 在 `role_permissions` 中分配权限

### 7.2 添加新权限

1. 在 `app_permissions` 枚举中添加新权限
2. 更新数据库架构
3. 为相关角色分配新权限
4. 在应用代码中实现权限检查

## 8. 常见问题与解决方案

### 8.1 用户无法访问预期功能

可能原因：
- 角色配置错误
- 权限检查逻辑有误
- 数据库角色关联丢失

解决方法：
- 检查用户角色分配
- 验证权限检查逻辑
- 确认数据库关联完整

### 8.2 权限变更未生效

可能原因：
- 缓存问题
- 会话未刷新
- 数据库更新失败

解决方法：
- 清除缓存
- 强制用户重新登录
- 验证数据库更新是否成功

## 9. 结论

本项目的认证与授权系统提供了全面的身份验证和访问控制功能，满足 SaaS 应用的安全需求。通过结合 Supabase Auth 的认证能力和自定义实现的基于角色的访问控制，系统能够有效管理用户身份和资源访问权限，为多租户应用提供坚实的安全基础。
