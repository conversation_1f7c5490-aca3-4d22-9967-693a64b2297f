import { LoaderFunctionArgs } from "@remix-run/node";
import { createId } from '@paralleldrive/cuid2';

// 临时存储音频数据的内存缓存
// 注意：这种方法只适合开发环境或小规模应用
// 生产环境应考虑使用更可靠的缓存方案(Redis等)
const audioCache = new Map<string, {
  data: Buffer,
  contentType: string,
  createdAt: number
}>();

// 定期清理超过10分钟的缓存项
setInterval(() => {
  const now = Date.now();
  for (const [id, entry] of audioCache.entries()) {
    if (now - entry.createdAt > 10 * 60 * 1000) {
      audioCache.delete(id);
      console.log(`[DEBUG] 已清理过期音频缓存: ${id}`);
    }
  }
}, 60 * 1000);

/**
 * 保存音频数据到缓存中
 */
export function saveAudioToCache(audioData: Buffer, contentType: string): string {
  const id = createId();
  audioCache.set(id, {
    data: audioData,
    contentType,
    createdAt: Date.now()
  });
  console.log(`[DEBUG] 已缓存音频数据: ${id}, 大小: ${audioData.length} 字节, 类型: ${contentType}`);
  return id;
}

/**
 * 从缓存中获取音频数据
 */
export function getAudioFromCache(id: string) {
  return audioCache.get(id);
}

/**
 * 加载器函数 - 将音频数据作为流返回
 */
export async function loader({ params, request }: LoaderFunctionArgs) {
  const id = params.id;
  
  console.log(`[DEBUG] 请求音频: ${id}`);
  
  if (!id) {
    return new Response("Audio ID required", { status: 400 });
  }
  
  try {
    const audioEntry = getAudioFromCache(id);
    
    if (!audioEntry) {
      console.log(`[DEBUG] 音频未找到或已过期: ${id}`);
      return new Response("Audio not found or expired", { status: 404 });
    }
    
    const totalSize = audioEntry.data.length;
    console.log(`[DEBUG] 返回音频数据, 大小: ${totalSize} 字节, 类型: ${audioEntry.contentType}`);
    
    // 设置更简单的响应头，避免复杂处理
    return new Response(audioEntry.data, {
      headers: {
        "Content-Type": audioEntry.contentType,
        "Content-Length": totalSize.toString(),
        "Cache-Control": "public, max-age=3600",
        "Access-Control-Allow-Origin": "*"
      },
      status: 200
    });
  } catch (error) {
    console.error(`[ERROR] 处理音频请求时出错:`, error);
    return new Response(`Error processing audio: ${error instanceof Error ? error.message : 'Unknown error'}`, { 
      status: 500,
      headers: {
        "Content-Type": "text/plain",
        "Access-Control-Allow-Origin": "*"
      }
    });
  }
}

// 导出默认空组件，这个路由只用于返回数据，不渲染内容
export default function AudioRoute() {
  return null;
} 