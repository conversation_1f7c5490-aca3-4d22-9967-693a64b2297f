{"home": {"pageTitle": "Home"}, "settings": {"pageTitle": "Settings", "pageDescription": "Manage your Team details", "teamLogo": "Team Logo", "teamLogoDescription": "Update your team's logo to make it easier to identify", "teamName": "Team Name", "teamNameDescription": "Update your team's name", "dangerZone": "Danger Zone", "dangerZoneDescription": "This section contains actions that are irreversible"}, "members": {"pageTitle": "Members"}, "billing": {"pageTitle": "Billing"}, "yourTeams": "Your Teams ({{teamsCount}})", "createTeam": "Create a Team", "creatingTeam": "Creating Team...", "personalAccount": "Personal Account", "searchAccount": "Search Account...", "membersTabLabel": "Members", "memberName": "Name", "youLabel": "You", "emailLabel": "Email", "roleLabel": "Role", "primaryOwnerLabel": "Primary Owner", "joinedAtLabel": "Joined at", "invitedAtLabel": "Invited at", "inviteMembersPageSubheading": "Invite members to your Team", "createTeamModalHeading": "Create Team", "createTeamModalDescription": "Create a new Team to manage your projects and members.", "teamNameLabel": "Team Name", "teamNameDescription": "Your team name should be unique and descriptive", "createTeamSubmitLabel": "Create Team", "createTeamSuccess": "Team created successfully", "createTeamError": "Team not created. Please try again.", "createTeamLoading": "Creating team...", "settingsPageLabel": "General", "createTeamDropdownLabel": "New team", "changeRole": "Change Role", "removeMember": "Remove from Account", "inviteMembersSuccess": "Members invited successfully!", "inviteMembersError": "Sorry, we encountered an error! Please try again", "inviteMembersLoading": "Inviting members...", "removeInviteButtonLabel": "Remove invite", "addAnotherMemberButtonLabel": "Add another one", "inviteMembersButtonLabel": "Send Invites", "removeMemberModalHeading": "You are removing this user", "removeMemberModalDescription": "Remove this member from the team. They will no longer have access to the team.", "removeMemberSuccessMessage": "Member removed successfully", "removeMemberErrorMessage": "Sorry, we encountered an error. Please try again", "removeMemberErrorHeading": "Sorry, we couldn't remove the selected member.", "removeMemberLoadingMessage": "Removing member...", "removeMemberSubmitLabel": "<PERSON><PERSON><PERSON> User from Team", "chooseDifferentRoleError": "Role is the same as the current one", "updateRole": "Update Role", "updateRoleLoadingMessage": "Updating role...", "updateRoleSuccessMessage": "Role updated successfully", "updatingRoleErrorMessage": "Sorry, we encountered an error. Please try again.", "updateMemberRoleModalHeading": "Update Member's Role", "updateMemberRoleModalDescription": "Change the role of the selected member. The role determines the permissions of the member.", "roleMustBeDifferent": "Role must be different from the current one", "memberRoleInputLabel": "Member role", "updateRoleDescription": "Pick a role for this member.", "updateRoleSubmitLabel": "Update Role", "transferOwnership": "Transfer Ownership", "transferOwnershipDescription": "Transfer ownership of the team to another member.", "transferOwnershipInputLabel": "Please type TRANSFER to confirm the transfer of ownership.", "transferOwnershipInputDescription": "By transferring ownership, you will no longer be the primary owner of the team.", "deleteInvitation": "Delete Invitation", "deleteInvitationDialogDescription": "You are about to delete the invitation. The user will no longer be able to join the team.", "deleteInviteSuccessMessage": "Invite deleted successfully", "deleteInviteErrorMessage": "Invite not deleted. Please try again.", "deleteInviteLoadingMessage": "Deleting invite. Please wait...", "confirmDeletingMemberInvite": "You are deleting the invite to <b>{{ email }}</b>", "transferOwnershipDisclaimer": "You are transferring ownership of the selected team to <b>{{ member }}</b>.", "transferringOwnership": "Transferring ownership...", "transferOwnershipSuccess": "Ownership successfully transferred", "transferOwnershipError": "Sorry, we could not transfer ownership to the selected member. Please try again.", "deleteInviteSubmitLabel": "Delete Invite", "youBadgeLabel": "You", "updateTeamLoadingMessage": "Updating Team...", "updateTeamSuccessMessage": "Team successfully updated", "updateTeamErrorMessage": "Could not update <PERSON>. Please try again.", "updateLogoErrorMessage": "Could not update <PERSON><PERSON>. Please try again.", "teamNameInputLabel": "Team Name", "teamLogoInputHeading": "Upload your team's Logo", "teamLogoInputSubheading": "Please choose a photo to upload as your team logo.", "updateTeamSubmitLabel": "Update Team", "inviteMembersHeading": "Invite Members to your Team", "inviteMembersDescription": "Invite members to your team by entering their email and role.", "emailPlaceholder": "<EMAIL>", "membersPageHeading": "Members", "inviteMembersButton": "Invite Members", "invitingMembers": "Inviting members...", "inviteMembersSuccessMessage": "Members invited successfully", "inviteMembersErrorMessage": "Sorry, members could not be invited. Please try again.", "pendingInvitesHeading": "Pending Invites", "pendingInvitesDescription": " Here you can manage the pending invitations to your team.", "noPendingInvites": "No pending invites found", "loadingMembers": "Loading members...", "loadMembersError": "Sorry, we couldn't fetch your team's members.", "loadInvitedMembersError": "Sorry, we couldn't fetch your team's invited members.", "loadingInvitedMembers": "Loading invited members...", "invitedBadge": "Invited", "duplicateInviteEmailError": "You have already entered this email address", "invitingOwnAccountError": "Hey, that's your email!", "dangerZone": "Danger Zone", "dangerZoneSubheading": "Delete or leave your team", "deleteTeam": "Delete Team", "deleteTeamDescription": "This action cannot be undone. All data associated with this team will be deleted.", "deletingTeam": "Deleting team", "deleteTeamModalHeading": "Deleting Team", "deletingTeamDescription": "You are about to delete the team {{ teamName }}. This action cannot be undone.", "deleteTeamInputField": "Type the name of the team to confirm", "leaveTeam": "Leave Team", "leavingTeamModalHeading": "Leaving Team", "leavingTeamModalDescription": "You are about to leave this team. You will no longer have access to it.", "leaveTeamDescription": "Click the button below to leave the team. Remember, you will no longer have access to it and will need to be re-invited to join.", "deleteTeamDisclaimer": "You are deleting the team {{ teamName }}. This action cannot be undone.", "leaveTeamDisclaimer": "You are leaving the team {{ teamName }}. You will no longer have access to it.", "deleteTeamErrorHeading": "Sorry, we couldn't delete your team.", "leaveTeamErrorHeading": "Sorry, we couldn't leave your team.", "searchMembersPlaceholder": "Search members", "createTeamErrorHeading": "Sorry, we couldn't create your team.", "createTeamErrorMessage": "We encountered an error creating your team. Please try again.", "transferTeamErrorHeading": "Sorry, we couldn't transfer ownership of your team.", "transferTeamErrorMessage": "We encountered an error transferring ownership of your team. Please try again.", "updateRoleErrorHeading": "Sorry, we couldn't update the role of the selected member.", "updateRoleErrorMessage": "We encountered an error updating the role of the selected member. Please try again.", "searchInvitations": "Search Invitations", "updateInvitation": "Update Invitation", "removeInvitation": "Remove Invitation", "acceptInvitation": "Accept Invitation", "renewInvitation": "Renew Invitation", "resendInvitation": "Resend Invitation", "expiresAtLabel": "Expires at", "expired": "Expired", "active": "Active", "inviteStatus": "Status", "inviteNotFoundOrExpired": "Invite not found or expired", "inviteNotFoundOrExpiredDescription": "The invite you are looking for is either expired or does not exist. Please contact the team owner to renew the invite.", "backToHome": "Back to Home", "renewInvitationDialogDescription": "You are about to renew the invitation to {{ email }}. The user will be able to join the team.", "renewInvitationErrorTitle": "Sorry, we couldn't renew the invitation.", "renewInvitationErrorDescription": "We encountered an error renewing the invitation. Please try again.", "signInWithDifferentAccount": "Sign in with a different account", "signInWithDifferentAccountDescription": "If you wish to accept the invitation with a different account, please sign out and back in with the account you wish to use.", "acceptInvitationHeading": "Accept Invitation to join {{accountName}}", "acceptInvitationDescription": "You have been invited to join the team {{accountName}}. If you wish to accept the invitation, please click the button below.", "continueAs": "Continue as {{email}}", "joinTeamAccount": "Join Team", "joiningTeam": "Joining team...", "leaveTeamInputLabel": "Please type LEA<PERSON> to confirm leaving the team.", "leaveTeamInputDescription": "By leaving the team, you will no longer have access to it.", "reservedNameError": "This name is reserved. Please choose a different one."}