{"name": "web", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "start:with-env": "pnpm with-env pnpm start", "with-env": "dotenv -e ./.env --", "typecheck": "tsc", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "supabase": "supabase", "supabase:start": "supabase status || supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts", "supabase:db:dump:local": "supabase db dump --local --data-only"}, "dependencies": {"@edge-csrf/core": "2.5.2", "@hookform/resolvers": "^3.10.0", "@kit/accounts": "workspace:*", "@kit/admin": "workspace:*", "@kit/ai": "workspace:*", "@kit/analytics": "workspace:*", "@kit/auth": "workspace:*", "@kit/billing": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/cms": "workspace:*", "@kit/csrf": "workspace:*", "@kit/database-webhooks": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/i18n": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/notifications": "workspace:*", "@kit/shared": "workspace:*", "@kit/storage": "workspace:*", "@kit/supabase": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/ui": "workspace:*", "@marsidev/react-turnstile": "^1.1.0", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-icons": "^1.3.2", "@remix-run/node": "^2.16.6", "@remix-run/react": "2.15.0", "@remix-run/serve": "^2.16.6", "@remix-run/server-runtime": "^2.16.6", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "5.61.5", "@tanstack/react-table": "^8.21.3", "@vercel/remix": "^2.15.3", "ai": "^2.2.37", "date-fns": "^4.1.0", "isbot": "^5.1.28", "lucide-react": "^0.462.0", "next-themes": "0.4.3", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "recharts": "^2.15.3", "zod": "^3.24.4"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@remix-run/dev": "^2.16.6", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "dotenv-cli": "^7.4.4", "eslint": "^8.57.1", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "supabase": "1.223.10", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=18.0.0"}}