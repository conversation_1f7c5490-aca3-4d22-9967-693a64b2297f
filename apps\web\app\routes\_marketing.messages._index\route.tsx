import { MetaFunction } from '@remix-run/react';

import { SitePageHeader } from '~/routes/_marketing/_components/site-page-header';

export const meta: MetaFunction = () => {
  return [
    {
      title: "Mother's Day Messages - Heartfelt Words for Mom",
      description: "Create beautiful, personalized messages for <PERSON>'s Day that express your love and appreciation.",
    },
  ];
};

export default function MessagesPage() {
  return (
    <div className="flex flex-col space-y-12">
      <SitePageHeader 
        title="Mother's Day Messages" 
        subtitle="Create beautiful, personalized messages that express your love and appreciation"
      />

      <div className="container mx-auto pb-16">
        <div className="bg-pink-50 dark:bg-purple-900/20 p-8 rounded-2xl text-center">
          <h2 className="text-2xl font-bold mb-4">Coming Soon!</h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            We're developing our AI-powered message generator to help you create the perfect 
            Mother's Day card, letter, or social media post. Check back soon to create heartfelt 
            messages that will make your mom feel truly special.
          </p>
        </div>
      </div>
    </div>
  );
}
