import { Form, useNavigate, useSubmit } from "@remix-run/react"
import { Search } from "lucide-react"
import { useEffect, useRef } from "react"

interface SearchBarProps {
    defaultValue?: string
}

export function SearchBar({ defaultValue = "" }: SearchBarProps) {
    const formRef = useRef<HTMLFormElement>(null)
    const inputRef = useRef<HTMLInputElement>(null)
    const submit = useSubmit()
    const navigate = useNavigate()

    // Auto-submit the form 500ms after the user stops typing
    useEffect(() => {
        const form = formRef.current
        const input = inputRef.current

        if (!form || !input) return

        const handleChange = (event: Event) => {
            const target = event.target as HTMLInputElement
            const debounceTimeout = setTimeout(() => {
                submit(form)
            }, 500)

            return () => clearTimeout(debounceTimeout)
        }

        input.addEventListener("input", handleChange)
        return () => input.removeEventListener("input", handleChange)
    }, [submit])

    return (
        <Form
            ref={formRef}
            method="get"
            className="relative"
            onChange={(event) => {
                // Reset to default view if search box is cleared
                const formData = new FormData(event.currentTarget)
                const searchQuery = formData.get("q")
                if (searchQuery === "") {
                    navigate("/home/<USER>")
                }
            }}
        >
            <div className="relative">
                <input
                    ref={inputRef}
                    type="text"
                    name="q"
                    placeholder="Search audio, scenes, or moods..."
                    defaultValue={defaultValue}
                    className="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={18} className="text-gray-400" />
                </div>
            </div>
        </Form>
    )
}

