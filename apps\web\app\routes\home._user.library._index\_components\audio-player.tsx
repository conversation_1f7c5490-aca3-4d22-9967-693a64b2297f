import type React from "react"

import { useState, useEffect, useRef } from "react"
import { X, Play, Pause, Volume2, VolumeX, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Repeat, Heart } from "lucide-react"
import type { AudioItem } from "./types"

interface AudioPlayerProps {
    audio: AudioItem
    onClose: () => void
}

export function AudioPlayer({ audio, onClose }: AudioPlayerProps) {
    const [isPlaying, setIsPlaying] = useState(false)
    const [currentTime, setCurrentTime] = useState(0)
    const [volume, setVolume] = useState(0.8)
    const [isMuted, setIsMuted] = useState(false)
    const [isLooping, setIsLooping] = useState(false)
    const [isFavorite, setIsFavorite] = useState(audio.isFavorite)

    const audioRef = useRef<HTMLAudioElement>(null)

    // Format time in MM:SS format
    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60)
        const secs = Math.floor(seconds % 60)
        return `${mins}:${secs.toString().padStart(2, "0")}`
    }

    // Toggle play/pause
    const togglePlayPause = () => {
        if (audioRef.current) {
            if (isPlaying) {
                audioRef.current.pause()
            } else {
                audioRef.current.play()
            }
            setIsPlaying(!isPlaying)
        }
    }

    // Handle time update
    const handleTimeUpdate = () => {
        if (audioRef.current) {
            setCurrentTime(audioRef.current.currentTime)
        }
    }

    // Handle seeking
    const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newTime = Number.parseFloat(e.target.value)
        setCurrentTime(newTime)
        if (audioRef.current) {
            audioRef.current.currentTime = newTime
        }
    }

    // Handle volume change
    const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newVolume = Number.parseFloat(e.target.value)
        setVolume(newVolume)
        if (audioRef.current) {
            audioRef.current.volume = newVolume
            setIsMuted(newVolume === 0)
        }
    }

    // Toggle mute
    const toggleMute = () => {
        if (audioRef.current) {
            if (isMuted) {
                audioRef.current.volume = volume
            } else {
                audioRef.current.volume = 0
            }
            setIsMuted(!isMuted)
        }
    }

    // Toggle loop
    const toggleLoop = () => {
        if (audioRef.current) {
            audioRef.current.loop = !isLooping
            setIsLooping(!isLooping)
        }
    }

    // Toggle favorite
    const toggleFavorite = () => {
        setIsFavorite(!isFavorite)
        // In a real app, you would update this in your database
    }

    // Skip backward 15 seconds
    const skipBackward = () => {
        if (audioRef.current) {
            audioRef.current.currentTime = Math.max(0, audioRef.current.currentTime - 15)
        }
    }

    // Skip forward 15 seconds
    const skipForward = () => {
        if (audioRef.current) {
            audioRef.current.currentTime = Math.min(audio.duration, audioRef.current.currentTime + 15)
        }
    }

    // Clean up on unmount
    useEffect(() => {
        return () => {
            if (audioRef.current) {
                audioRef.current.pause()
            }
        }
    }, [])

    return (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg">
            <audio
                ref={audioRef}
                src={`/audio/${audio.id}.mp3`} // This would be the actual audio URL in a real app
                onTimeUpdate={handleTimeUpdate}
                onEnded={() => setIsPlaying(false)}
                loop={isLooping}
            />

            <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                    <div className="h-12 w-12 bg-gray-100 rounded mr-3"></div>
                    <div>
                        <h3 className="font-medium text-gray-900">{audio.title}</h3>
                        <p className="text-sm text-gray-500">
                            {formatTime(currentTime)} / {formatTime(audio.duration)}
                        </p>
                    </div>
                </div>

                <div className="flex items-center space-x-4">
                    <button
                        onClick={toggleFavorite}
                        className={`${isFavorite ? "text-red-500" : "text-gray-400"} hover:text-red-500`}
                    >
                        <Heart size={20} fill={isFavorite ? "currentColor" : "none"} />
                    </button>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-900">
                        <X size={20} />
                    </button>
                </div>
            </div>

            <div className="space-y-2">
                <input
                    type="range"
                    min={0}
                    max={audio.duration}
                    value={currentTime}
                    onChange={handleSeek}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />

                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                        <button onClick={toggleMute} className="text-gray-600 hover:text-gray-900">
                            {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                        </button>
                        <input
                            type="range"
                            min={0}
                            max={1}
                            step={0.01}
                            value={isMuted ? 0 : volume}
                            onChange={handleVolumeChange}
                            className="w-20 h-1.5 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                    </div>

                    <div className="flex items-center space-x-4">
                        <button onClick={skipBackward} className="text-gray-600 hover:text-gray-900">
                            <SkipBack size={24} />
                        </button>
                        <button
                            onClick={togglePlayPause}
                            className="h-10 w-10 rounded-full bg-purple-600 flex items-center justify-center"
                        >
                            {isPlaying ? <Pause size={20} fill="white" /> : <Play size={20} className="ml-1" fill="white" />}
                        </button>
                        <button onClick={skipForward} className="text-gray-600 hover:text-gray-900">
                            <SkipForward size={24} />
                        </button>
                    </div>

                    <button
                        onClick={toggleLoop}
                        className={`${isLooping ? "text-purple-600" : "text-gray-600"} hover:text-purple-600`}
                    >
                        <Repeat size={20} />
                    </button>
                </div>
            </div>
        </div>
    )
}
