// 改进错误处理
export class AIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public cause?: unknown
  ) {
    super(message);
    this.name = 'AIError';
  }
}

export function handleAIError(error: unknown): AIError {
  // 转换不同类型的错误为统一格式
  if (error instanceof AIError) return error;
  
  // OpenAI SDK 特定错误
  if (error && typeof error === 'object' && 'cause' in error) {
    const cause = (error as any).cause;
    
    // 网络连接错误
    if (cause && typeof cause === 'object' && 'code' in cause) {
      if (cause.code === 'ETIMEDOUT') {
        return new AIError('连接超时，请检查您的网络连接或代理设置', 503, error);
      }
      if (cause.code === 'ECONNREFUSED') {
        return new AIError('连接被拒绝，请检查服务地址是否正确', 503, error);
      }
    }
    
    // API 认证错误
    if ('status' in error && (error as any).status === 401) {
      return new AIError('API 密钥无效或过期', 401, error);
    }
  }
  
  // 默认错误
  console.error('处理未知错误:', error);
  return new AIError('AI 服务调用过程中发生未知错误', 500, error);
} 