import { describe, it, expect, vi, beforeEach } from 'vitest';
import { OpenAIService } from '../services/openai.server';
import { DeepseekService } from '../services/deepseek.server';
import { TaskManager } from '../tasks/taskManager.server';

// Mock OpenAI and other external dependencies
vi.mock('openai', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: vi.fn().mockResolvedValue({
            choices: [{ message: { content: 'Mocked OpenAI response' } }],
            model: 'gpt-4o',
            usage: { total_tokens: 100 }
          })
        }
      },
      embeddings: {
        create: vi.fn().mockResolvedValue({
          data: [{ embedding: [0.1, 0.2, 0.3] }]
        })
      }
    }))
  };
});

describe('OpenAI Service', () => {
  let openAIService: OpenAIService;

  beforeEach(() => {
    // Reset mocks between tests
    vi.clearAllMocks();
    openAIService = new OpenAIService('test-api-key');
  });

  it('should generate a text response', async () => {
    const response = await openAIService.generateResponse('Test prompt');
    
    expect(response).toEqual({
      text: 'Mocked OpenAI response',
      provider: 'openai',
      metadata: {
        model: 'gpt-4o',
        usage: { total_tokens: 100 }
      }
    });
  });

  it('should generate chat completions', async () => {
    const result = await openAIService.chatCompletion([
      { role: 'user', content: 'Hello' }
    ]);
    
    expect(result).toBe('Mocked OpenAI response');
  });

  it('should generate embeddings', async () => {
    const embeddings = await openAIService.generateEmbedding('Test text');
    expect(embeddings).toEqual([0.1, 0.2, 0.3]);
  });

  it('should handle errors properly', async () => {
    // Mock an error response
    const errorMock = vi.spyOn(console, 'error').mockImplementation(() => {});
    const mockOpenAI = require('openai').default;
    mockOpenAI.mockImplementationOnce(() => ({
      chat: {
        completions: {
          create: vi.fn().mockRejectedValue(new Error('API error'))
        }
      }
    }));

    await expect(
      new OpenAIService('test-api-key').generateResponse('Test prompt')
    ).rejects.toThrow('Failed to generate AI response from OpenAI: API error');
    
    expect(errorMock).toHaveBeenCalled();
    errorMock.mockRestore();
  });
});

describe('TaskManager', () => {
  let taskManager: TaskManager;

  beforeEach(() => {
    taskManager = new TaskManager();
  });

  it('should create a task with an ID', () => {
    const taskId = taskManager.createTask('test', { foo: 'bar' });
    expect(taskId).toBeDefined();
    expect(typeof taskId).toBe('string');
  });

  it('should update task status', () => {
    const taskId = taskManager.createTask('test', {});
    taskManager.setTaskStatus(taskId, 'completed');
    const status = taskManager.getTaskStatus(taskId);
    expect(status.status).toBe('completed');
  });

  it('should set task results', () => {
    const taskId = taskManager.createTask('test', {});
    const result = { data: 'test result' };
    taskManager.setTaskResult(taskId, result);
    const status = taskManager.getTaskStatus(taskId);
    expect(status.result).toEqual(result);
  });

  it('should handle errors', () => {
    const taskId = taskManager.createTask('test', {});
    taskManager.setTaskError(taskId, 'Test error');
    const status = taskManager.getTaskStatus(taskId);
    expect(status.status).toBe('error');
    expect(status.error).toBe('Test error');
  });
});
