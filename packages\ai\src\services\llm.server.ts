import type { AIResponse, ChatMessage } from "../types";
import { deepseekService } from "./deepseek.server";

// Define the interface for AI providers
interface AIProvider {
    generateResponse(prompt: string, options?: any): Promise<AIResponse>;
    chatCompletion(messages: ChatMessage[], options?: any): Promise<string>;
    generateEmbedding(text: string, model?: string): Promise<number[]>;
    streamChatCompletion(messages: ChatMessage[], options?: any): AsyncGenerator<string, void, unknown>;
}

/**
 * LLMService 配置接口
 */
export interface LLMServiceConfig {
  defaultProvider?: string;
  defaultModel?: string;
  defaultTemperature?: number;
  defaultMaxTokens?: number;
}

/**
 * 通用的大语言模型服务
 * 默认使用 DeepseekService，但也可以根据环境变量选择不同的后端服务
 */
export class LLMService {
    private provider: AIProvider;
    private defaultModel: string;

    constructor(
        provider: string = process.env.DEFAULT_LLM_PROVIDER || 'deepseek',
        model?: string
    ) {
        switch (provider.toLowerCase()) {
            case 'deepseek':
                this.provider = deepseekService;
                // 使用 Deepseek 的默认模型
                this.defaultModel = model || deepseekService['DEEPSEEK_MODELS'].chat;
                break;
            // ... 其他 provider cases ...
            default:
                throw new Error(`Unsupported LLM provider: ${provider}`);
        }

        console.log(`LLM 服务初始化完成，使用默认提供商: ${provider}`);
        console.log(`默认模型: ${this.defaultModel}`);
    }

    /**
     * 生成文本响应
     * @param prompt 提示文本
     * @param options 选项
     * @returns AI响应
     */
    async generateResponse(prompt: string, options: {
        model?: string;
        temperature?: number;
        maxTokens?: number;
        provider?: string;
    } = {}): Promise<AIResponse> {
        // 使用指定提供商或默认提供商
        const provider = options.provider?.toLowerCase() || this.defaultModel;

        try {
            // 目前只支持 deepseek
            if (provider === 'deepseek') {
                return await deepseekService.generateResponse(prompt, options);
            }

            return {
                text: `不支持的 LLM 提供商: ${provider}`,
                provider: "error",
                metadata: { error: "UNSUPPORTED_PROVIDER" }
            };
        } catch (error) {
            console.error(`LLM 响应生成错误 (${provider}):`, error);
            return {
                text: "AI 服务调用失败，请检查配置或稍后再试",
                provider: "error",
                metadata: { error: "SERVICE_ERROR" }
            };
        }
    }

    /**
     * 生成聊天完成
     * @param messages 消息数组
     * @param options 选项
     * @returns 完成文本
     */
    async chatCompletion(
        messages: ChatMessage[],
        options: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            systemPrompt?: string;
        } = {}
    ): Promise<string> {
        try {
            // 如果没有指定模型，使用默认模型
            const modelToUse = options.model || this.defaultModel;
            return await this.provider.chatCompletion(messages, {
                ...options,
                model: modelToUse
            });
        } catch (error) {
            console.error('LLM 聊天完成错误:', error);
            return "AI 服务调用失败，请检查配置或稍后再试";
        }
    }

    /**
     * 生成文本嵌入
     * @param text 输入文本
     * @param model 模型
     * @param provider 提供商
     * @returns 嵌入向量
     */
    async generateEmbedding(text: string, model?: string, provider?: string): Promise<number[]> {
        provider = provider?.toLowerCase() || this.defaultModel;

        try {
            if (provider === 'deepseek') {
                return await deepseekService.generateEmbedding(text, model || 'deepseek-embedding');
            }

            console.warn(`不支持的嵌入提供商: ${provider}`);
            return [];
        } catch (error) {
            console.error(`生成嵌入错误 (${provider}):`, error);
            return [];
        }
    }

    /**
     * 生成流式聊天响应
     */
    async *streamChatCompletion(
        messages: ChatMessage[],
        options: {
            model?: string;
            temperature?: number;
            maxTokens?: number;
            systemPrompt?: string;
            provider?: string;
        } = {}
    ): AsyncGenerator<string, void, unknown> {
        const provider = options.provider?.toLowerCase() || this.defaultModel;

        try {
            if (provider === 'deepseek') {
                // 假设 deepseekService 有相应的流式方法
                for await (const chunk of deepseekService.streamChatCompletion(messages, options)) {
                    yield chunk;
                }
            } else {
                yield `不支持的 LLM 提供商: ${provider}`;
            }
        } catch (error) {
            console.error(`LLM 流式聊天错误 (${provider}):`, error);
            yield "AI 服务调用失败，请检查配置或稍后再试";
        }
    }
}

// 创建默认实例
export const llmService = new LLMService();