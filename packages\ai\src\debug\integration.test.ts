import { describe, it, expect, vi, beforeAll, afterAll } from 'vitest';
import { workflowEngine } from '../workflows/engine.server';
import { translateWorkflow } from '../workflows/translateWorkflow';
import { openAIService } from '../services/openai.server';
import { taskManager } from '../tasks/taskManager.server';

// 模拟外部服务
vi.mock('../services/openai.server', () => {
  return {
    openAIService: {
      generateResponse: vi.fn().mockResolvedValue({
        text: '你好世界，这是一个测试。',
        provider: 'openai',
        metadata: { model: 'gpt-4o' }
      }),
      chatCompletion: vi.fn().mockResolvedValue('Mock chat completion response')
    }
  };
});

describe('AI Module Integration', () => {
  // 捕获控制台输出以便于调试
  let consoleLogSpy: any;
  let consoleErrorSpy: any;

  beforeAll(() => {
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    consoleLogSpy.mockRestore();
    consoleErrorSpy.mockRestore();
  });

  it('should execute a complete translation workflow', async () => {
    const context = await workflowEngine.execute(translateWorkflow, {
      text: 'Hello world, this is a test.',
      sourceLanguage: 'English',
      targetLanguage: 'Chinese'
    });

    expect(context.translatedText).toBe('你好世界，这是一个测试。');
    expect(openAIService.generateResponse).toHaveBeenCalledWith(
      expect.stringContaining('Hello world, this is a test.')
    );
  });

  it('should create and execute a translation task', async () => {
    const taskId = taskManager.createTask('translation', {
      text: 'Hello world',
      sourceLanguage: 'English',
      targetLanguage: 'Chinese'
    });

    // 检查任务是否已创建
    const status = taskManager.getTaskStatus(taskId);
    expect(status.status).toBe('pending');
    
    // 执行翻译任务
    const result = await taskManager.translateTask(
      'Hello world',
      'English',
      'Chinese'
    );

    expect(result).toBe('你好世界，这是一个测试。');
    
    // 验证任务状态和结果
    const completedStatus = taskManager.getTaskStatus(taskId);
    expect(completedStatus.status).toBe('completed');
    expect(completedStatus.result).toHaveProperty('translatedText');
  });
});
