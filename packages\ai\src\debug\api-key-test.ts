/**
 * This test file verifies that the AI services handle missing API keys gracefully
 */

import { openAIService } from '../services/openai.server';
import { deepseekService } from '../services/deepseek.server';
import { xaiService } from '../services/xai.server';
import { llmService } from '../services/llm.server';

async function testOpenAIWithoutKey() {
  console.log('\n=== Testing OpenAI Service without API key ===');
  try {
    // Save original env var
    const originalKey = process.env.OPENAI_API_KEY;
    // Unset the key
    process.env.OPENAI_API_KEY = '';
    
    // Create a new instance without API key
    const testService = new (openAIService.constructor as any)();
    
    // Test generateResponse
    const response = await testService.generateResponse('Hello, world!');
    console.log('Response:', response);
    
    // Test chatCompletion
    const chatResponse = await testService.chatCompletion([{ role: 'user', content: 'Hello' }]);
    console.log('Chat response:', chatResponse);
    
    // Test generateEmbedding
    const embeddings = await testService.generateEmbedding('Test text');
    console.log('Embeddings length:', embeddings.length);
    
    // Restore original key
    process.env.OPENAI_API_KEY = originalKey;
    
    return true;
  } catch (error) {
    console.error('OpenAI test failed:', error);
    return false;
  }
}

async function testDeepseekWithoutKey() {
  console.log('\n=== Testing Deepseek Service without API key ===');
  try {
    // Save original env var
    const originalKey = process.env.DEEPSEEK_API_KEY;
    // Unset the key
    process.env.DEEPSEEK_API_KEY = '';
    
    // Create a new instance without API key
    const testService = new (deepseekService.constructor as any)();
    
    // Test generateResponse
    const response = await testService.generateResponse('Hello, world!');
    console.log('Response:', response);
    
    // Test chatCompletion
    const chatResponse = await testService.chatCompletion([{ role: 'user', content: 'Hello' }]);
    console.log('Chat response:', chatResponse);
    
    // Test generateEmbedding
    const embeddings = await testService.generateEmbedding('Test text');
    console.log('Embeddings length:', embeddings.length);
    
    // Restore original key
    process.env.DEEPSEEK_API_KEY = originalKey;
    
    return true;
  } catch (error) {
    console.error('Deepseek test failed:', error);
    return false;
  }
}

async function testXAIWithoutKey() {
  console.log('\n=== Testing XAI Service without API key ===');
  try {
    // Save original env var
    const originalKey = process.env.XAI_API_KEY;
    // Unset the key
    process.env.XAI_API_KEY = '';
    
    // Create a new instance without API key
    const testService = new (xaiService.constructor as any)();
    
    // Test generateImage
    const imageResponse = await testService.generateImage('A beautiful landscape');
    console.log('Image response:', imageResponse);
    
    // Test generateChatCompletion
    const chatResponse = await testService.generateChatCompletion([{ role: 'user', content: 'Hello' }]);
    console.log('Chat response:', chatResponse);
    
    // Restore original key
    process.env.XAI_API_KEY = originalKey;
    
    return true;
  } catch (error) {
    console.error('XAI test failed:', error);
    return false;
  }
}

async function testLLMServiceWithoutKeys() {
  console.log('\n=== Testing LLM Service without API keys ===');
  try {
    // Save original env vars
    const originalOpenAIKey = process.env.OPENAI_API_KEY;
    const originalDeepseekKey = process.env.DEEPSEEK_API_KEY;
    
    // Unset the keys
    process.env.OPENAI_API_KEY = '';
    process.env.DEEPSEEK_API_KEY = '';
    
    // Test generateResponse
    const response = await llmService.generateResponse('Hello, world!');
    console.log('Response:', response);
    
    // Test chatCompletion
    const chatResponse = await llmService.chatCompletion([{ role: 'user', content: 'Hello' }]);
    console.log('Chat response:', chatResponse);
    
    // Restore original keys
    process.env.OPENAI_API_KEY = originalOpenAIKey;
    process.env.DEEPSEEK_API_KEY = originalDeepseekKey;
    
    return true;
  } catch (error) {
    console.error('LLM Service test failed:', error);
    return false;
  }
}

async function runTests() {
  console.log('Starting API key missing tests...');
  
  const openaiResult = await testOpenAIWithoutKey();
  const deepseekResult = await testDeepseekWithoutKey();
  const xaiResult = await testXAIWithoutKey();
  const llmResult = await testLLMServiceWithoutKeys();
  
  console.log('\n=== Test Results ===');
  console.log('OpenAI Service:', openaiResult ? 'PASS' : 'FAIL');
  console.log('Deepseek Service:', deepseekResult ? 'PASS' : 'FAIL');
  console.log('XAI Service:', xaiResult ? 'PASS' : 'FAIL');
  console.log('LLM Service:', llmResult ? 'PASS' : 'FAIL');
}

// Run the tests
runTests().catch(console.error);
