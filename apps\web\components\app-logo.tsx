import { Link } from '@remix-run/react';

import { cn } from '@kit/ui/utils';

function LogoImage({
  className,
  width = 105,
}: {
  className?: string;
  width?: number;
}) {
  return (
    <svg
      width={width}
      className={cn(`w-[80px] lg:w-[95px]`, className)}
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#EC4899" /> {/* Pink-500 */}
          <stop offset="100%" stopColor="#9333EA" /> {/* Purple-600 */}
        </linearGradient>
        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur stdDeviation="3" result="blur" />
          <feComposite in="SourceGraphic" in2="blur" operator="over" />
        </filter>
      </defs>

      <circle cx="100" cy="100" r="95" fill="#FDF2F8" className="dark:fill-[#4B0082]" /> {/* Pink-50 / Indigo */}
      <circle cx="100" cy="100" r="90" fill="#FCE7F3" className="dark:fill-[#5B0E9B]" /> {/* Pink-100 / Indigo lighter */}

      {/* Heart shape */}
      <path
        d="M100 160C100 160 60 130 60 95C60 80 70 65 90 65C100 65 100 75 100 75C100 75 100 65 110 65C130 65 140 80 140 95C140 130 100 160 100 160Z"
        fill="url(#logoGradient)"
        filter="url(#glow)"
      />

      {/* Mother's Day text */}
      <g filter="url(#glow)">
        <path
          d="M50 180L60 180M60 180L70 180M60 180V170"
          stroke="url(#logoGradient)"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M80 180C80 175 85 170 90 170C95 170 100 175 100 180"
          stroke="url(#logoGradient)"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M110 170V180"
          stroke="url(#logoGradient)"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M120 170V180C120 175 125 170 130 170C135 170 140 175 140 180"
          stroke="url(#logoGradient)"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M150 170V180"
          stroke="url(#logoGradient)"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
}

export function AppLogo({
  href,
  label,
  className,
}: {
  href?: string;
  className?: string;
  label?: string;
}) {
  return (
    <Link aria-label={label ?? 'Home Page'} to={href ?? '/'}>
      <LogoImage className={className} />
    </Link>
  );
}
