{"name": "@kit/database-webhooks", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/billing": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/stripe": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/tsconfig": "workspace:*", "@supabase/supabase-js": "^2.49.4", "zod": "^3.24.4"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}