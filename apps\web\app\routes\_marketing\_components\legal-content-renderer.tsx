import { lazy } from 'react';

import styles from '../../_marketing.blog.$slug/_components/html-renderer.module.css';

const ContentRenderer = lazy(() => {
  return import('@kit/cms').then((mod) => ({ default: mod.ContentRenderer }));
});

interface LegalContentRendererProps {
  content: unknown;
}

export function LegalContentRenderer({ content }: LegalContentRendererProps) {
  if (!content) {
    return null;
  }

  return (
    <div className="mx-auto max-w-3xl">
      <article className={styles.HTML}>
        <ContentRenderer content={content} />
      </article>
    </div>
  );
} 