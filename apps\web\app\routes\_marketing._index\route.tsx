import { Link } from '@remix-run/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Heart, Gift, MessageSquare, Calendar, <PERSON>rk<PERSON>, Flower } from 'lucide-react';
import type React from "react";

import { PricingTable } from '@kit/billing-gateway/marketing';
import {
  <PERSON><PERSON>B<PERSON>on,
  <PERSON>,
  Pill,
  SecondaryHero,
} from '@kit/ui/marketing';

import billingConfig from '~/config/billing.config';
import pathsConfig from '~/config/paths.config';

// Create a simplified version of GradientSecondaryText
const GradientSecondaryText = ({ children }: { children: React.ReactNode }) => (
  <span className="bg-gradient-to-r from-pink-500 to-purple-600 dark:from-pink-400 dark:to-purple-400 bg-clip-text text-transparent">
    {children}
  </span>
)

export default function Index() {
  return (
    <div className={'mt-4 flex flex-col space-y-24 py-14'}>
      <Hero
        pill={
          <Pill label={'MOTHER\'S DAY SPECIAL'}>
            <span>Create perfect personalized gifts for Mom with the help of AI</span>
          </Pill>
        }
        title={
          <>
            <span>Make Mom's Day</span>
            <span>Special with AI</span>
          </>
        }
        subtitle={
          <span>
            Mother's Day GPT helps you create heartfelt, personalized gifts, messages, and experiences
            for your mom. Our AI-powered platform generates unique content tailored to your mother's
            personality and your special relationship.
          </span>
        }
        cta={<MainCallToActionButton />}
        image={
          <div
            className={
              'dark:border-primary/10 rounded-2xl border border-pink-100 bg-pink-50 dark:bg-purple-900/20 flex items-center justify-center p-8 h-[400px] overflow-hidden'
            }
          >
            <img
              src="/images/landingpage/mothers-day-hero.webp"
              alt="Mother's Day GPT interface showing personalized gift ideas, message templates, and creative suggestions for Mother's Day"
              className="w-full h-full object-contain"
            />
          </div>
        }
      />

      <FeatureSection />

      <div className="container mx-auto bg-gradient-to-r from-pink-50 to-purple-50 dark:from-pink-950/30 dark:to-purple-950/30 rounded-2xl p-8">
        <div className="flex flex-col space-y-8 md:flex-row md:space-y-0 md:space-x-8 items-center">
          <div className="md:w-1/2">
            <h3 className="text-2xl font-bold mb-4">Why Choose Mother's Day GPT?</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="bg-pink-100 dark:bg-pink-900/50 p-2 rounded-full mr-3 mt-1">
                  <Sparkles className="h-4 w-4 text-pink-600 dark:text-pink-300" />
                </div>
                <div>
                  <h4 className="font-medium">Personalized to Your Mom</h4>
                  <p className="text-gray-600 dark:text-gray-300">Our AI creates unique content based on your mom's personality, interests, and your relationship with her</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-pink-100 dark:bg-pink-900/50 p-2 rounded-full mr-3 mt-1">
                  <Gift className="h-4 w-4 text-pink-600 dark:text-pink-300" />
                </div>
                <div>
                  <h4 className="font-medium">Creative Gift Ideas</h4>
                  <p className="text-gray-600 dark:text-gray-300">Get unique gift suggestions that go beyond flowers and chocolates, tailored to your budget and mom's preferences</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-pink-100 dark:bg-pink-900/50 p-2 rounded-full mr-3 mt-1">
                  <Heart className="h-4 w-4 text-pink-600 dark:text-pink-300" />
                </div>
                <div>
                  <h4 className="font-medium">Heartfelt Messages</h4>
                  <p className="text-gray-600 dark:text-gray-300">Generate beautiful, emotional messages that express your love and appreciation in ways that will touch her heart</p>
                </div>
              </li>
            </ul>
          </div>
          <div className="md:w-1/2 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md">
            <div className="text-center mb-4">
              <span className="inline-block bg-pink-100 dark:bg-pink-900/50 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm font-medium">User Feedback</span>
            </div>
            <blockquote className="italic text-gray-700 dark:text-gray-300 mb-4">
              "I was struggling to find the right words for my mom's card. Mother's Day GPT helped me create a beautiful message that made her cry happy tears. The gift suggestions were perfect too - I found something she truly loved!"
            </blockquote>
            <div className="flex items-center">
              <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 mr-3"></div>
              <div>
                <p className="font-medium">Michael T.</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">Mother's Day GPT user</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={'container mx-auto'}>
        <div
          className={
            'flex flex-col items-center justify-center space-y-16 py-16'
          }
        >
          <SecondaryHero
            pill={<Pill>Mother's Day Special Offer</Pill>}
            heading="Special Mother's Day Packages"
            subheading="Choose the perfect package to create an unforgettable Mother's Day experience."
          />

          <div className={'w-full'}>
            <PricingTable
              config={billingConfig}
              paths={{
                signUp: pathsConfig.auth.signUp,
                return: pathsConfig.app.home,
              }}
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto bg-pink-50 dark:bg-purple-900/20 rounded-2xl p-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold mb-2">Frequently Asked Questions</h3>
          <p className="text-gray-600 dark:text-gray-300">Common questions about Mother's Day GPT</p>
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">How does Mother's Day GPT create personalized content?</h4>
            <p className="text-gray-600 dark:text-gray-300">Our AI analyzes the information you provide about your mom's personality, interests, and your relationship to generate unique messages, gift ideas, and experiences tailored specifically to her.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">Can I edit the messages that are generated?</h4>
            <p className="text-gray-600 dark:text-gray-300">Absolutely! You can edit, customize, and personalize any content our AI generates. Think of our suggestions as a starting point that you can refine to make it perfect for your mom.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">How far in advance should I start planning?</h4>
            <p className="text-gray-600 dark:text-gray-300">We recommend starting at least a week before Mother's Day, especially if you're planning to order physical gifts. However, our platform can help with last-minute ideas too, including digital gifts and experiences.</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm">
            <h4 className="font-medium mb-2">What's included in each package?</h4>
            <p className="text-gray-600 dark:text-gray-300">Our basic package includes message generation and simple gift ideas. Premium packages offer more sophisticated content creation, custom poetry, personalized gift guides based on budget, and digital gift creation tools.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function FeatureSection() {
  return (
    <div className={"container mx-auto"}>
      <div className={"flex flex-col space-y-16 xl:space-y-32 2xl:space-y-36"}>
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center gap-2 bg-pink-50 dark:bg-pink-950/30 px-4 py-2 rounded-full mb-4">
            <Heart className="h-4 w-4 text-pink-600 dark:text-pink-400" />
            <span className="text-sm font-medium text-pink-600 dark:text-pink-400">Complete Mother's Day Solution</span>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="font-semibold dark:text-white">AI-powered tools to create the perfect Mother's Day</span>
          </h2>

          <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
            <GradientSecondaryText>
              Combining artificial intelligence with deep personalization to help you create
              meaningful gifts, heartfelt messages, and memorable experiences for Mom.
            </GradientSecondaryText>
          </p>
        </div>

        {/* Feature Cards - Using a cleaner grid layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {/* Feature 1: Personalized Messages */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img
                src="/images/landingpage/mothers-day-messages.webp"
                alt="Beautiful Mother's Day card with personalized message"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-pink-100 dark:bg-pink-900/50 p-2 rounded-full">
                  <MessageSquare className="h-5 w-5 text-pink-600 dark:text-pink-400" />
                </div>
                <h3 className="text-xl font-bold">Heartfelt Messages</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Our AI helps you craft beautiful, personalized messages that express your love and appreciation.
                From touching cards to emotional letters, create content that will make Mom feel truly special
                and understood.
              </p>
            </div>
          </div>

          {/* Feature 2: Gift Ideas */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img
                src="/images/landingpage/mothers-day-gifts.webp"
                alt="Personalized Mother's Day gift ideas"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-purple-100 dark:bg-purple-900/50 p-2 rounded-full">
                  <Gift className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-xl font-bold">Personalized Gift Ideas</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Get creative, thoughtful gift suggestions tailored to your mom's personality, interests, and your budget.
                Our AI analyzes your input to recommend unique gifts that go beyond the usual flowers and chocolates.
              </p>
            </div>
          </div>

          {/* Feature 3: Experience Planning */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img
                src="/images/landingpage/mothers-day-experiences.webp"
                alt="Mother and child enjoying a special experience together"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-full">
                  <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-bold">Experience Planning</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Create memorable experiences for Mother's Day with our detailed planning assistance.
                From breakfast in bed to special outings, get step-by-step guides and creative ideas
                to make the day unforgettable.
              </p>
            </div>
          </div>

          {/* Feature 4: Digital Creations */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden transition-all hover:shadow-2xl hover:-translate-y-1">
            <div className="aspect-video w-full relative">
              <img
                src="/images/landingpage/mothers-day-digital.webp"
                alt="Digital Mother's Day creation with photos and text"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-red-100 dark:bg-red-900/50 p-2 rounded-full">
                  <Sparkles className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <h3 className="text-xl font-bold">Digital Creations</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Create beautiful digital gifts like personalized poems, custom artwork, photo collages,
                and video messages. Our tools help you design professional-looking digital creations
                that can be shared instantly or printed as keepsakes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function MainCallToActionButton() {
  return (
    <div className={'flex space-x-4'}>
      <CtaButton className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white">
        <Link to={'/auth/sign-up'}>
          <span className={'flex items-center space-x-0.5'}>
            <span>
              Create Mother's Day Magic
            </span>

            <ArrowRightIcon
              className={
                'animate-in fade-in slide-in-from-left-8 h-4' +
                ' zoom-in fill-mode-both delay-1000 duration-1000'
              }
            />
          </span>
        </Link>
      </CtaButton>

      <CtaButton variant={'outline'} className="border-pink-300 text-pink-700 hover:bg-pink-50 dark:border-pink-700 dark:text-pink-300 dark:hover:bg-pink-950/30">
        <Link to={'/contact'}>
          Learn More
        </Link>
      </CtaButton>
    </div>
  );
}
