interface ApiConfig {
  apiKey?: string;
  baseUrl?: string;
  version?: string;
  organization?: string;
}

export interface Config {
  openai?: ApiConfig;
  deepseek?: ApiConfig;
  xai?: ApiConfig;
}

// 从环境变量、配置文件或其他来源获取配置
export function getConfig(): Config {
  // 确保在 Node.js 环境中
  const processEnv = typeof process !== 'undefined' ? process.env : {};
  
  return {
    openai: {
      apiKey: processEnv.OPENAI_API_KEY || '',
      baseUrl: processEnv.OPENAI_BASE_URL || 'https://api.openai.com/v1',
      organization: processEnv.OPENAI_ORGANIZATION || '',
    },
    deepseek: {
      apiKey: processEnv.DEEPSEEK_API_KEY || '',
      baseUrl: processEnv.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',
    },
    xai: {
      apiKey: processEnv.XAI_API_KEY || '',
      baseUrl: processEnv.XAI_BASE_URL || 'https://api.xai.com/v1',
    },
    // 其他服务配置...
  };
}

// 为了向后兼容，保留单独的获取函数
export function getOpenAIApiKey(): string | undefined {
  return typeof process !== 'undefined' ? process.env.OPENAI_API_KEY : undefined;
}

export function getDeepseekApiKey(): string | undefined {
  return typeof process !== 'undefined' ? process.env.DEEPSEEK_API_KEY : undefined;
}

export function getXAIApiKey(): string | undefined {
  return typeof process !== 'undefined' ? process.env.XAI_API_KEY : undefined;
} 