# Supabase 自定义钩子（Hooks）文档

## 概述

Supabase 是项目的核心后端服务，提供了数据库、认证和实时功能。为了更方便地在前端组件中使用 Supabase 的功能，项目实现了一系列自定义 React 钩子（Hooks）。这些钩子封装了 Supabase 客户端的常用操作，使开发过程更加简洁和高效。

## 核心钩子

### 1. useSupabase

```typescript
function useSupabase<Db = Database>() {
  return useMemo(() => getSupabaseBrowserClient<Db>(), []);
}
```

**功能**：提供对 Supabase 客户端的访问，是其他钩子的基础。

**使用场景**：
- 当需要直接访问 Supabase 客户端进行自定义操作时
- 作为其他 Supabase 相关钩子的基础

**示例**：
```tsx
const client = useSupabase();
// 使用 client 调用 Supabase 的各种 API
```

## 用户认证钩子

### 2. useAuthChangeListener

```typescript
function useAuthChangeListener({
  privatePathPrefixes = PRIVATE_PATH_PREFIXES,
  appHomePath,
  onEvent,
}: {
  appHomePath: string;
  privatePathPrefixes?: string[];
  onEvent?: (event: AuthChangeEvent, user: Session | null) => void;
})
```

**功能**：监听用户认证状态变化，自动处理路由重定向。

**参数**：
- `privatePathPrefixes`：受保护的路由前缀列表，默认包括 '/home', '/admin', '/join', '/update-password'
- `appHomePath`：应用的主页路径
- `onEvent`：认证状态变化时的回调函数

**使用场景**：
- 在应用的根组件中使用，以监听全局认证状态变化
- 当用户退出登录时，自动重定向到公共页面
- 当未登录用户访问私有路由时，自动重定向到首页

### 3. useUser

```typescript
function useUser(initialData?: User | null) {
  // ...
}
```

**功能**：获取当前登录用户的信息，集成了 React Query 进行状态管理。

**参数**：
- `initialData`：可选的初始用户数据

**返回值**：
- 包含用户信息的 React Query 查询结果对象

**使用场景**：
- 在需要获取当前用户信息的组件中使用
- 用于基于用户信息条件渲染界面元素

**示例**：
```tsx
const { data: user, isLoading } = useUser();

if (isLoading) {
  return <LoadingSpinner />;
}

if (!user) {
  return <NotLoggedInMessage />;
}

return <UserDashboard user={user} />;
```

### 4. useSignInWithEmailPassword

```typescript
function useSignInWithEmailPassword() {
  // ...
}
```

**功能**：使用邮箱和密码进行用户登录，封装了登录逻辑和错误处理。

**返回值**：
- 包含 `mutate` 方法的 React Query Mutation 对象，用于触发登录请求

**使用场景**：
- 在登录表单中处理用户登录

**示例**：
```tsx
const signInMutation = useSignInWithEmailPassword();

const handleSubmit = async (values) => {
  try {
    await signInMutation.mutateAsync({
      email: values.email,
      password: values.password
    });
    // 登录成功处理
  } catch (error) {
    // 登录失败处理
  }
};
```

### 5. useSignUpWithEmailAndPassword

```typescript
function useSignUpWithEmailAndPassword() {
  // ...
}
```

**功能**：使用邮箱和密码进行用户注册，处理注册流程和验证。

**返回值**：
- 包含 `mutate` 方法的 React Query Mutation 对象，用于触发注册请求

**参数**：（传递给 mutate 方法的对象）
- `email`：用户邮箱
- `password`：用户密码
- `emailRedirectTo`：邮箱验证后的重定向 URL
- `captchaToken`：可选的验证码令牌

**使用场景**：
- 在注册表单中处理用户注册

**示例**：
```tsx
const signUpMutation = useSignUpWithEmailAndPassword();

const handleRegister = async (values) => {
  try {
    await signUpMutation.mutateAsync({
      email: values.email,
      password: values.password,
      emailRedirectTo: `${window.location.origin}/auth/callback`
    });
    // 注册成功处理
  } catch (error) {
    // 注册失败处理
  }
};
```

### 6. useSignOut

**功能**：处理用户退出登录操作。

**使用场景**：
- 在用户菜单或设置页面中添加退出登录功能

**示例**：
```tsx
const signOut = useSignOut();

const handleSignOut = async () => {
  await signOut.mutateAsync();
  // 重定向或显示退出成功消息
};
```

### 7. useSignInWithOtp

**功能**：使用一次性密码（OTP）进行无密码登录。

**使用场景**：
- 提供无密码登录选项，通过邮箱获取验证码

### 8. useSignInWithProvider

**功能**：使用第三方提供商（如 Google、Facebook 等）进行社交登录。

**使用场景**：
- 提供社交登录选项

### 9. useVerifyOtp

**功能**：验证一次性密码（OTP），通常用于邮箱验证或两步验证。

**使用场景**：
- 在邮箱验证流程中验证用户输入的 OTP 码

## 其他实用钩子

### 10. useRequestResetPassword

**功能**：发送密码重置请求。

**使用场景**：
- 在忘记密码流程中，请求发送重置密码的邮件

### 11. useFetchMfaFactors

**功能**：获取用户的多因素认证因子。

**使用场景**：
- 在安全设置中显示和管理用户的多因素认证选项

### 12. useUpdateUserMutation

**功能**：更新用户信息。

**使用场景**：
- 在用户资料设置中更新用户数据

## 钩子的集成和使用最佳实践

1. **集中管理认证状态**：
   - 在应用的根组件使用 `useAuthChangeListener` 监听全局认证状态变化
   - 创建一个认证上下文(Context)，使用 `useUser` 提供全局用户状态

2. **表单集成**：
   - 将认证钩子与表单库（如 Formik 或 React Hook Form）结合使用
   - 利用 mutation 的状态（如 `isLoading`、`isError`）更新 UI

3. **错误处理**：
   - 使用 `try/catch` 包装 mutation 调用，处理特定错误
   - 实现一致的错误展示方式

4. **路由保护**：
   - 结合 `useUser` 和路由守卫，保护私有路由
   - 在路由组件中检查用户状态，未登录时重定向

5. **性能优化**：
   - 使用 React Query 的缓存能力避免不必要的请求
   - 合理设置查询配置，如 `staleTime` 和 `cacheTime`

## 高级用例

### 自定义认证流程

可以通过组合多个钩子创建自定义认证流程，例如：

1. 使用 `useSignInWithEmailPassword` 进行首次登录
2. 检测是否需要两步验证，使用 `useFetchMfaFactors` 获取可用的验证方式
3. 使用 `useVerifyOtp` 完成两步验证流程

### 多租户支持

结合用户信息和其他业务逻辑，可以实现多租户支持：

1. 使用 `useUser` 获取用户信息
2. 根据用户所属的组织或团队，调整应用行为和数据访问
3. 在 API 请求中包含适当的租户信息

## 调试和常见问题

1. **认证状态不同步**：
   - 使用 `useAuthChangeListener` 确保在认证状态变化时正确更新 UI
   - 确保 React Query 的缓存策略适合你的应用

2. **重定向循环**：
   - 检查路由保护逻辑，确保不会在认证检查和重定向之间形成循环
   - 使用条件渲染而非过早重定向

3. **会话过期处理**：
   - 实现全局错误处理，捕获 401 错误并提示用户重新登录
   - 使用 refresh token 机制自动续期会话

## 结论

Supabase 自定义钩子（Hooks）提供了一套完整的工具，简化了与 Supabase 服务的交互过程。通过这些钩子，开发者可以轻松实现用户认证、会话管理和数据访问等功能，同时保持代码的可读性和可维护性。

在开发新功能时，建议优先考虑使用这些现有钩子，而不是直接操作 Supabase 客户端，以保持代码的一致性和降低重复代码的可能性。
