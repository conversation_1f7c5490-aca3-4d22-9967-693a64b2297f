// 定义明确的类型
export interface AIMessage {
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
  name?: string;
  id?: string;
  createdAt?: Date;
}

// 为了兼容性，使用 ChatMessage 作为 AIMessage 的别名
export type ChatMessage = AIMessage;

// API 响应类型
export interface AIResponse {
  text: string;
  provider: string;
  metadata?: any;
}

// 工作流相关类型
export interface WorkflowContext {
  [key: string]: any;
}

export interface Workflow {
  name: string;
  execute: (context: WorkflowContext) => Promise<WorkflowContext>;
}

// 任务相关类型
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed';

export interface Task {
  id: string;
  type: string;
  status: TaskStatus;
  params: any;
  result?: any;
  error?: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatCompletionOptions {
  model: string;
  temperature?: number;
  maxTokens?: number;
  // 其他选项...
}

export interface LLMOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  provider?: string;
  systemPrompt?: string;
}

export interface LLMResponse extends AIResponse {
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
} 