import { json, type ActionFunctionArgs } from '@remix-run/node';
import { Form, useActionData } from '@remix-run/react';
import { Button } from '@kit/ui';
import { uploadAudio } from '~/lib/cloudflare-r2';

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.formData();
    const audioFile = formData.get('audioFile') as File;
    
    if (!audioFile) {
      return json({ success: false, error: 'No audio file provided' });
    }

    // Convert the file to a buffer
    const arrayBuffer = await audioFile.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Generate a unique filename
    const filename = `${Date.now()}-${audioFile.name}`;
    const filePath = `audio/${filename}`;
    
    // Upload to Cloudflare R2
    await uploadAudio(filePath, buffer);
    
    return json({ success: true, filePath });
  } catch (error) {
    console.error('Error uploading audio:', error);
    return json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    });
  }
}

export default function AudioUploadExample() {
  const actionData = useActionData<typeof action>();
  
  return (
    <div className="py-8 px-4 max-w-3xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Audio Upload Example</h1>
      
      <Form method="post" encType="multipart/form-data" className="space-y-4">
        <div>
          <label htmlFor="audioFile" className="block text-sm font-medium mb-1">
            Select Audio File
          </label>
          <input
            id="audioFile"
            name="audioFile"
            type="file"
            accept="audio/*"
            className="w-full border rounded p-2"
            required
          />
        </div>
        
        <Button type="submit">Upload Audio</Button>
      </Form>
      
      {actionData && (
        <div className="mt-6">
          {actionData.success ? (
            <div className="bg-green-50 border border-green-200 rounded p-4">
              <p className="text-green-800 font-medium">Upload successful!</p>
              <p className="mt-2">File path: {actionData.filePath}</p>
            </div>
          ) : (
            <div className="bg-red-50 border border-red-200 rounded p-4">
              <p className="text-red-800 font-medium">Upload failed</p>
              <p className="mt-2">Error: {actionData.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
