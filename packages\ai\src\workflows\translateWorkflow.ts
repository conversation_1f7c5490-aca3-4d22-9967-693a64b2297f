import { openAIService } from "../services/openai.server";
import type { Workflow, WorkflowContext } from "../types";

export const translateWorkflow: Workflow = {
    name: "Translate",
    execute: async (context: WorkflowContext) => {
        // Validate input
        if (!context.text || !context.sourceLanguage || !context.targetLanguage) {
            throw new Error("Missing required parameters for translation: text, sourceLanguage, targetLanguage");
        }

        console.log(`Translating text from ${context.sourceLanguage} to ${context.targetLanguage}`);
        
        try {
            const translationResponse = await openAIService.generateResponse(
                `Translate the following text from ${context.sourceLanguage} to ${context.targetLanguage}:\n\n${context.text}`
            );
            
            context.translatedText = translationResponse.text;
            return context;
        } catch (error) {
            console.error("Translation error:", error);
            throw new Error(`Translation failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    },
};
