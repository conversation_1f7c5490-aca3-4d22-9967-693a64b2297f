// Predefined list of prompt suggestions
const prompts = [
    "Create a calm forest rain ambiance to help me sleep",
    "I'm stressed and need soothing music to relax",
    "Create a beach environment with gentle waves",
    "I need meditation background music that's calm and non-distracting",
    "I have insomnia and need white noise to help me sleep",
    "Create a warm cafe atmosphere with soft background music",
]

interface QuickPromptsProps {
    onSelect: (prompt: string) => void
}

const QuickPrompts = ({ onSelect }: QuickPromptsProps) => {
    return (
        <div className="space-y-2">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {prompts.map((prompt) => (
                    <button
                        key={prompt}
                        className="justify-start h-auto py-1.5 px-3 text-xs font-normal rounded-md border border-input bg-background hover:bg-accent hover:text-accent-foreground text-left"
                        onClick={() => onSelect(prompt)}
                        type="button"
                    >
                        {prompt.length > 25 ? `${prompt.substring(0, 25)}...` : prompt}
                    </button>
                ))}
            </div>
            
            <button 
                className="h-auto p-0 text-xs text-primary underline-offset-4 hover:underline"
                onClick={() => {}}
                type="button"
            >
                View More Prompts
            </button>
        </div>
    )
}

export default QuickPrompts
