import { useEffect, useRef } from "react"

interface AudioVisualizerProps {
    isPlaying: boolean
}

const AudioVisualizer = ({ isPlaying }: AudioVisualizerProps) => {
    const canvasRef = useRef<HTMLCanvasElement>(null)

    useEffect(() => {
        if (!canvasRef.current) return

        const canvas = canvasRef.current
        const ctx = canvas.getContext("2d")
        if (!ctx) return

        // Set canvas dimensions and handle resize
        const setCanvasDimensions = () => {
            canvas.width = canvas.offsetWidth
            canvas.height = canvas.offsetHeight
        }

        setCanvasDimensions()
        window.addEventListener("resize", setCanvasDimensions)

        // Animation configuration
        let animationId: number
        const bars = 60 // Number of visualization bars
        const barWidth = canvas.width / bars - 2

        // Animation loop
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height)

            if (isPlaying) {
                // Generate random heights for visualization bars
                for (let i = 0; i < bars; i++) {
                    const height = isPlaying ? Math.random() * canvas.height * 0.8 : 2

                    // Create gradient for bars
                    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
                    gradient.addColorStop(0, "hsl(var(--primary) / 0.8)") // Use primary color
                    gradient.addColorStop(1, "hsl(var(--primary) / 0.4)") // Use primary color with opacity

                    ctx.fillStyle = gradient

                    // Draw visualization bar
                    ctx.fillRect(i * (barWidth + 2), canvas.height - height, barWidth, height)
                }
            } else {
                // Draw flat line when not playing
                ctx.beginPath()
                ctx.moveTo(0, canvas.height / 2)
                ctx.lineTo(canvas.width, canvas.height / 2)
                ctx.strokeStyle = "hsl(var(--primary) / 0.6)"
                ctx.lineWidth = 2
                ctx.stroke()
            }

            animationId = requestAnimationFrame(animate)
        }

        animate()

        // Cleanup
        return () => {
            cancelAnimationFrame(animationId)
            window.removeEventListener("resize", setCanvasDimensions)
        }
    }, [isPlaying])

    return (
        <div className="w-full h-16 bg-muted/30 border rounded-md overflow-hidden">
            <canvas ref={canvasRef} className="w-full h-full" aria-label="Audio visualization" />
        </div>
    )
}

export default AudioVisualizer