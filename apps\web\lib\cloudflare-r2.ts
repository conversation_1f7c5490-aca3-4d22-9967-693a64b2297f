import { createCloudflareR2Client, uploadAudioToR2, type CloudflareR2Config } from '@kit/storage';

// Load environment variables
const r2Config: CloudflareR2Config = {
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID || '',
  accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID || '',
  secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY || '',
  bucketName: process.env.CLOUDFLARE_BUCKET_NAME || '',
};

// Create R2 client
export const r2Client = createCloudflareR2Client(r2Config);

/**
 * Upload audio file to Cloudflare R2
 * @param filePath Path where the file will be stored in the bucket
 * @param fileBuffer Audio file buffer
 * @returns Result of the upload operation
 */
export async function uploadAudio(filePath: string, fileBuffer: Buffer) {
  if (!r2Config.accountId || !r2Config.accessKeyId || !r2Config.secretAccessKey || !r2Config.bucketName) {
    throw new Error('Cloudflare R2 credentials not configured');
  }

  return uploadAudioToR2(
    r2Client,
    r2Config.bucketName,
    filePath,
    fileBuffer
  );
}

/**
 * Generate a URL for the uploaded audio file
 * @param filePath Path where the file is stored in the bucket
 * @returns URL to access the file
 */
export function getAudioUrl(filePath: string) {
  if (!r2Config.accountId || !r2Config.bucketName) {
    throw new Error('Cloudflare R2 credentials not configured');
  }
  
  // Replace with your actual Cloudflare domain if you have configured one
  return `https://${r2Config.bucketName}.${r2Config.accountId}.r2.cloudflarestorage.com/${filePath}`;
}
