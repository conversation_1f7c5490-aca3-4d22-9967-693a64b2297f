import { j<PERSON>, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node"
import { Form, useLoaderData, useSubmit, useNavigation, useSearchParams } from "@remix-run/react"
import { <PERSON>U<PERSON>, Filter } from "lucide-react"
import React, { useEffect, useState } from "react"
import { ClientOnly } from "@kit/ui/client-only"
import { PageBody } from "@kit/ui/page"
import { AppBreadcrumbs } from "@kit/ui/app-breadcrumbs"
import { HomeLayoutPageHeader } from "~/routes/home._user/_components/home-page-header"

import { CategoryNav } from "./_components/CategoryNav"
import { FeaturedCarousel } from "./_components/FeaturedCarousel"
import { ThemeCollection } from "./_components/ThemeCollection"
import { AudioGrid } from "./_components/AudioGrid"
import { FilterPanel } from "./_components/FilterPanel"
import { AudioPreviewPlayer } from "./_components/AudioPreviewPlayer"

// Types
export interface Audio {
    id: string
    title: string
    description?: string
    duration: string
    coverImage: string
    plays: number
    favorites: number
}

interface LoaderData {
    featuredAudios: Audio[]
    categories: { id: string; name: string }[]
    themeCollections: {
        id: string
        title: string
        audios: Audio[]
    }[]
    audios: Audio[]
    currentCategory: string
    query: string
    filters: {
        minDuration: number
        maxDuration: number
        types: string[]
        uploadTime: string
        popularity: string
    }
}

// Server-side loader function
export async function loader({ request }: LoaderFunctionArgs) {
    const url = new URL(request.url)
    const category = url.searchParams.get("category") || "popular"
    const query = url.searchParams.get("q") || ""
    const minDuration = Number.parseInt(url.searchParams.get("minDuration") || "0")
    const maxDuration = Number.parseInt(url.searchParams.get("maxDuration") || "3600")
    const types = url.searchParams.getAll("type")
    const uploadTime = url.searchParams.get("uploadTime") || "any"
    const popularity = url.searchParams.get("popularity") || "any"

    // Fetch data based on parameters
    const featuredAudios = await getFeaturedAudios()
    const categories = await getCategories()
    const themeCollections = await getThemeCollections()
    const audios = await getAudios({
        category,
        query,
        minDuration,
        maxDuration,
        types,
        uploadTime,
        popularity,
    })

    return json<LoaderData>({
        featuredAudios,
        categories,
        themeCollections,
        audios,
        currentCategory: category,
        query,
        filters: {
            minDuration,
            maxDuration,
            types,
            uploadTime,
            popularity,
        },
    })
}

// Server-side action function
export async function action({ request }: ActionFunctionArgs) {
    const formData = await request.formData()
    const intent = formData.get("intent")

    switch (intent) {
        case "favorite": {
            const audioId = formData.get("audioId")
            // In a real app, update database
            return json({ success: true })
        }
        case "add-to-library": {
            const audioId = formData.get("audioId")
            // In a real app, update database
            return json({ success: true })
        }
        default: {
            return json({ error: "Invalid action" }, { status: 400 })
        }
    }
}

// Root error boundary
export function ErrorBoundary() {
    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Something went wrong</h1>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
                We're having trouble loading the audio content. Please try again later.
            </p>
            <Form method="get">
                <button type="submit" className="px-4 py-2 bg-primary text-white rounded-lg">
                    Try Again
                </button>
            </Form>
        </div>
    )
}

export default function ExploreRoute() {
    const { featuredAudios, categories, themeCollections, audios, currentCategory, query, filters } =
        useLoaderData<typeof loader>()

    const [searchParams] = useSearchParams()
    const navigation = useNavigation()
    const submit = useSubmit()
    const [scrollPosition, setScrollPosition] = useState(0)
    const [isMounted, setIsMounted] = useState(false)

    const isLoading = navigation.state === "loading"
    const selectedAudioId = searchParams.get("playing")
    const selectedAudio = selectedAudioId ? audios.find((audio) => audio.id === selectedAudioId) : null

    // Handle audio selection
    const handleAudioSelect = (audio: Audio) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set("playing", audio.id)
        submit(newSearchParams)
    }

    // Use useEffect to handle client-side only code
    useEffect(() => {
        setIsMounted(true)
        
        const handleScroll = () => {
            setScrollPosition(window.scrollY)
        }
        
        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [])

    return (
        <>
            <HomeLayoutPageHeader
                title="Explore Audio"
                description={<AppBreadcrumbs />}
            >
                <Form method="get">
                    <input type="hidden" name="showFilters" value="true" />
                    <button type="submit" className="p-2 rounded-full bg-gray-100 dark:bg-gray-700">
                        <Filter size={20} className="text-gray-600 dark:text-gray-300" />
                    </button>
                </Form>
            </HomeLayoutPageHeader>

            <PageBody>
                <div className="flex w-full flex-1 flex-col">
                    <Form method="get" onChange={(e) => submit(e.currentTarget)} className="relative mb-4">
                        <input
                            type="search"
                            name="q"
                            placeholder="Search audio, scenes, or moods..."
                            defaultValue={query}
                            className="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                        />
                    </Form>

                    {/* Filter panel */}
                    {searchParams.get("showFilters") === "true" && <FilterPanel initialFilters={filters} />}

                    {/* Loading state */}
                    {isLoading && <LoadingOverlay />}

                    {/* Main content area */}
                    <div className="flex-1">
                        <FeaturedSection audios={featuredAudios} onSelect={handleAudioSelect} />
                        <CategorySection categories={categories} currentCategory={currentCategory} />
                        <ThemeCollectionsSection collections={themeCollections} onSelect={handleAudioSelect} />
                        <AllAudioSection audios={audios} onSelect={handleAudioSelect} />
                    </div>

                    <BottomNavigation />

                    {/* Audio preview player - Only render on client side */}
                    <ClientOnly>
                        {selectedAudio && (
                            <AudioPreviewPlayer
                                audio={selectedAudio}
                                onClose={() => {
                                    const newSearchParams = new URLSearchParams(searchParams)
                                    newSearchParams.delete("playing")
                                    submit(newSearchParams)
                                }}
                            />
                        )}
                    </ClientOnly>

                    <ClientOnly>
                        <ScrollToTopButton isVisible={scrollPosition > 300} />
                    </ClientOnly>
                </div>
            </PageBody>
        </>
    )
}

// Loading overlay component
function LoadingOverlay() {
    return (
        <div className="fixed inset-0 bg-white/50 dark:bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
        </div>
    )
}

// Featured section component
function FeaturedSection({ audios, onSelect }: { audios: Audio[], onSelect: (audio: Audio) => void }) {
    return (
        <section className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Featured</h2>
            <FeaturedCarousel audios={audios} onSelect={onSelect} />
        </section>
    )
}

// Category section component
function CategorySection({ categories, currentCategory }: { categories: { id: string; name: string }[], currentCategory: string }) {
    return (
        <section className="mb-8">
            <CategoryNav categories={categories} currentCategory={currentCategory} />
        </section>
    )
}

// Theme collections section component
function ThemeCollectionsSection({ 
    collections, 
    onSelect 
}: { 
    collections: { id: string; title: string; audios: Audio[] }[],
    onSelect: (audio: Audio) => void
}) {
    return (
        <>
            {collections.map((collection) => (
                <section key={collection.id} className="mb-8">
                    <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">{collection.title}</h2>
                    <AudioGrid audios={collection.audios} onSelect={onSelect} compact={true} />
                </section>
            ))}
        </>
    )
}

// All audio section component
function AllAudioSection({ audios, onSelect }: { audios: Audio[], onSelect: (audio: Audio) => void }) {
    return (
        <section className="mb-8">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">All Audio</h2>
            <AudioGrid audios={audios} onSelect={onSelect} compact={true} />
        </section>
    )
}

// Bottom navigation component
function BottomNavigation() {
    return (
        <footer className="sticky bottom-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
            <nav className="flex justify-around items-center">
                <Form method="get" action="/home">
                    <button className="flex flex-col items-center text-gray-600 dark:text-gray-300">
                        <span className="text-xs">Home</span>
                    </button>
                </Form>
                <Form method="get" action="/home/<USER>">
                    <button className="flex flex-col items-center text-primary">
                        <span className="text-xs">Explore</span>
                    </button>
                </Form>
                <Form method="get" action="/home/<USER>">
                    <button className="flex flex-col items-center text-gray-600 dark:text-gray-300">
                        <span className="text-xs">Create</span>
                    </button>
                </Form>
                <Form method="get" action="/home/<USER>">
                    <button className="flex flex-col items-center text-gray-600 dark:text-gray-300">
                        <span className="text-xs">Library</span>
                    </button>
                </Form>
            </nav>
        </footer>
    )
}

// Scroll to top button component
function ScrollToTopButton({ isVisible }: { isVisible: boolean }) {
    const handleScrollTop = () => {
        window.scrollTo({ top: 0, behavior: "smooth" })
    }

    return (
        <button
            onClick={handleScrollTop}
            className={`fixed bottom-20 right-4 p-3 rounded-full bg-primary text-white shadow-lg transition-opacity duration-200 hover:opacity-100 focus:opacity-100 ${isVisible ? 'opacity-100' : 'opacity-0'}`}
        >
            <ArrowUp size={20} />
        </button>
    )
}

// Mock data fetching functions (in real app, these would be database queries)
async function getFeaturedAudios() {
    return [
        {
            id: "1",
            title: "Rainy Forest",
            description: "Immerse yourself in the peaceful atmosphere of raindrops on leaves",
            duration: "45:00",
            coverImage: "/images/explore/rain-forest.webp",
            plays: 12500,
            favorites: 3200,
        },
        {
            id: "2",
            title: "Ocean Waves Meditation",
            description: "Listen to the soothing rhythm of waves hitting the shore",
            duration: "30:00",
            coverImage: "/images/explore/ocean-waves.webp",
            plays: 9800,
            favorites: 2100,
        },
        {
            id: "3",
            title: "City White Noise",
            description: "Distant city ambient sounds to help urban dwellers sleep",
            duration: "60:00",
            coverImage: "/images/explore/city-noise.webp",
            plays: 7600,
            favorites: 1800,
        },
    ]
}

async function getCategories() {
    return [
        { id: "popular", name: "Popular" },
        { id: "newest", name: "Newest" },
        { id: "nature", name: "Nature Sounds" },
        { id: "meditation", name: "Meditation" },
        { id: "deep-sleep", name: "Deep Sleep" },
        { id: "focus", name: "Focus" },
        { id: "relaxation", name: "Relaxation" },
    ]
}

async function getThemeCollections() {
    return [
        {
            id: "rainy-day",
            title: "Rainy Day Relaxation",
            audios: [
                {
                    id: "101",
                    title: "Rain on Window",
                    description: "The gentle patter of rain against your window",
                    duration: "45:00",
                    coverImage: "/images/explore/rain-window.webp",
                    plays: 5600,
                    favorites: 1200,
                },
                {
                    id: "102",
                    title: "Thunderstorm Night",
                    description: "Distant thunder with steady rainfall",
                    duration: "50:00",
                    coverImage: "/images/explore/thunderstorm.webp",
                    plays: 4800,
                    favorites: 980,
                },
                {
                    id: "103",
                    title: "Walking in Rain",
                    description: "The sound of footsteps in puddles and rain",
                    duration: "30:00",
                    coverImage: "/images/explore/walking-rain.webp",
                    plays: 3200,
                    favorites: 760,
                },
            ],
        },
        {
            id: "city-sounds",
            title: "City White Noise",
            audios: [
                {
                    id: "201",
                    title: "Cafe Ambience",
                    description: "The comforting buzz of a busy cafe",
                    duration: "60:00",
                    coverImage: "/images/explore/audio-1.webp",
                    plays: 7800,
                    favorites: 1500,
                },
                {
                    id: "202",
                    title: "Distant Traffic",
                    description: "The rhythmic flow of city traffic from afar",
                    duration: "45:00",
                    coverImage: "/images/explore/audio-2.webp",
                    plays: 4200,
                    favorites: 890,
                },
                {
                    id: "203",
                    title: "Office Environment",
                    description: "Subtle keyboard typing and office ambience",
                    duration: "55:00",
                    coverImage: "/images/explore/audio-3.webp",
                    plays: 3600,
                    favorites: 720,
                },
            ],
        },
    ]
}

interface GetAudiosParams {
    category: string
    query: string
    minDuration: number
    maxDuration: number
    types: string[]
    uploadTime: string
    popularity: string
}

async function getAudios(params: GetAudiosParams) {
    // In a real app, this would be a database query with filters
    return Array.from({ length: 12 }, (_, i) => ({
        id: `audio-${i + 1}`,
        title: `Audio Title ${i + 1}`,
        description: `This is a short description for audio ${i + 1}, introducing its features and use cases.`,
        duration: `${Math.floor(Math.random() * 60) + 10}:00`,
        coverImage: `/images/explore/audio-${(i % 5) + 1}.webp`,
        plays: Math.floor(Math.random() * 10000),
        favorites: Math.floor(Math.random() * 2000),
    }))
}

