{"name": "@kit/billing-gateway", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./checkout": "./src/components/embedded-checkout.tsx", "./marketing": "./src/components/marketing.tsx"}, "devDependencies": {"@hookform/resolvers": "^3.10.0", "@kit/billing": "workspace:*", "@kit/csrf": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/lemon-squeezy": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/stripe": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tailwind-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@remix-run/react": "2.15.0", "@supabase/supabase-js": "^2.49.4", "@types/react": "^18.3.21", "date-fns": "^4.1.0", "lucide-react": "^0.462.0", "react": "18.3.1", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "zod": "^3.24.4"}, "eslintConfig": {"root": true, "extends": ["@kit/eslint-config/base", "@kit/eslint-config/react"]}, "typesVersions": {"*": {"*": ["src/*"]}}}